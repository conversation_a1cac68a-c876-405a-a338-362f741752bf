#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'resume-ai-backend'))

from app.utils.resume_parser import ResumeParser
import re

# 测试文本
test_text = """# 工作经验

# 2019.07-至今

# 阿里巴巴集团

# 高级产品经理

•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率 $1 5 \%$ •带领5人产品团队，协调研发、设计、运营等多部门资
源，确保项目按时高质量交付•通过数据分析优化产品策略，年度GMV增长达 $3 0 \%$
•建了产品标准化流程，提高团队效率 $2 0 \%$

2017.07 - 2019.06

# 腾讯科技有限公司

# 产品经理

•负责微信小程序生态产品规划，参与设计小程序开放平台功能•主导了2个B端工具类小程序开发，累计服务企业用户10万+• 通过用户调研和数据 
分析，优化产品体验，NPS提升25分•协助制定小程序商业化策略，实现年收入增长 $5 0 \%$

# 技能特长

产品规划 需求分析 用户体验设计 数据分析 项目管理 Axure SQL Python

英语流利"""

def debug_work_parsing():
    print("🔍 调试工作经历解析...")
    print("=" * 50)
    
    # 1. 定位工作经验部分
    work_section_match = re.search(r'# 工作经验\s*\n([\s\S]*?)(?=\n# 技能特长|\n# 自我评价|$)', test_text)
    if not work_section_match:
        print("❌ 未找到工作经验部分")
        return
    
    work_section = work_section_match.group(1)
    print(f"✅ 找到工作经验部分，长度: {len(work_section)}")
    print("工作经验部分内容:")
    print(repr(work_section))
    print("\n" + "=" * 50)
    
    # 2. 按行分析
    lines = work_section.split('\n')
    print(f"总行数: {len(lines)}")
    for i, line in enumerate(lines):
        print(f"第{i}行: {repr(line)}")
    
    print("\n" + "=" * 50)
    
    # 3. 测试时间解析
    print("测试时间解析:")
    print(f"'2019.07-至今' -> {ResumeParser.parse_time_range('2019.07-至今')}")
    print(f"'2017.07 - 2019.06' -> {ResumeParser.parse_time_range('2017.07 - 2019.06')}")

    print("\n" + "=" * 50)

    # 4. 测试解析
    result = ResumeParser.parse_work_experience(test_text)
    print(f"解析结果: {result}")

    # 5. 手动模拟解析过程
    print("\n" + "=" * 50)
    print("手动模拟解析过程:")

    lines = work_section.split('\n')
    current_entry = {}
    content_lines = []

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        print(f"处理第{i}行: '{line}'")

        # 检查是否是时间行（以#开头且包含时间）
        if line.startswith('# ') and re.search(r'\d{4}\.\d{2}', line):
            print(f"  -> 识别为时间行")
            time_str = line[2:].strip()
            start_date, end_date = ResumeParser.parse_time_range(time_str)
            print(f"  -> 解析时间: {start_date} - {end_date}")
            current_entry["开始时间"] = start_date
            current_entry["结束时间"] = end_date
        # 检查是否是独立的时间行（不以#开头）
        elif re.search(r'\d{4}\.\d{2}\s*[-–]\s*\d{4}\.\d{2}', line):
            print(f"  -> 识别为独立时间行")
            start_date, end_date = ResumeParser.parse_time_range(line)
            print(f"  -> 解析时间: {start_date} - {end_date}")
            # 这里应该保存之前的条目并开始新条目
            if current_entry.get("公司") and current_entry.get("职位"):
                print(f"  -> 保存之前的条目: {current_entry}")
            current_entry = {"开始时间": start_date, "结束时间": end_date}
            content_lines = []
        elif line.startswith('# '):
            print(f"  -> 识别为标题行")
            title = line[2:].strip()
            if not current_entry.get("公司"):
                current_entry["公司"] = title
                print(f"  -> 设置公司: {title}")
            elif not current_entry.get("职位"):
                current_entry["职位"] = title
                print(f"  -> 设置职位: {title}")
        else:
            print(f"  -> 识别为内容行")
            content_lines.append(line)

        print(f"  -> 当前条目: {current_entry}")

    print(f"\n最终条目: {current_entry}")
    print(f"内容行数: {len(content_lines)}")

if __name__ == "__main__":
    debug_work_parsing()
