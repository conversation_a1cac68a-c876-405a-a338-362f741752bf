"""
简历解析器 - 基于demo的成功经验重构

功能:
1. 解析个人信息
2. 解析工作经历
3. 解析项目经验
4. 解析教育背景
5. 清理特殊符号
"""

import re
from typing import Dict, List, Any


class ResumeParser:
    """简历解析器"""
    
    @staticmethod
    def clean_special_symbols(text: str) -> str:
        """清理特殊符号的通用函数"""
        if not text:
            return text
        
        # 处理 ${数字+} 格式
        text = re.sub(r'\$\{\s*(\d+)\s*\+\s*\}', r'\1+', text)  # ${20+} -> 20+
        text = re.sub(r'\$\{\s*(\d+)\s*(\d+)\s*\+\s*\}', r'\1\2+', text)  # ${2 0+} -> 20+
        
        # 处理 ${数字} 格式
        text = re.sub(r'\$\{\s*(\d+)\s*(\d+)\s*\}', r'\1\2', text)  # ${1 5} -> 15
        text = re.sub(r'\$\{\s*(\d+)\s*\}', r'\1', text)  # ${15} -> 15
        
        # 处理 $+$ 格式
        text = re.sub(r'\$\+\$', '+', text)  # $+$ -> +
        
        # 处理百分比格式
        text = re.sub(r'\$\s*(\d+)\s*\\\s*%\s*\$', r'\1%', text)  # $4 5 \%$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\\\s*%\s*\$', r'\1\2%', text)  # $2 8 \%$ -> 28%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\s*\\\s*%\s*\$', r'\1\2%', text)  # $3 0 \%$ -> 30%
        
        # 处理双反斜杠格式
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\\\\\s*%\s*\$', r'\1\2%', text)  # $4 5 \\%$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*\\\\\s*%\s*\$', r'\1%', text)  # $5 \\%$ -> 5%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\s*\\\\\s*%\s*\$', r'\1\2%', text)  # $5 0 \\%$ -> 50%
        
        # 处理不带反斜杠的格式
        text = re.sub(r'\$\s*(\d+)\s*%\s*\$', r'\1%', text)  # $45 %$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*%\s*\$', r'\1\2%', text)  # $28 %$ -> 28%
        
        # 处理万+格式
        text = re.sub(r'(\d+)万\s*\$\+\$', r'\1万+', text)  # 10万 $+$ -> 10万+
        
        # 处理数字+$格式
        text = re.sub(r'(\d+)\+\$', r'\1+', text)  # 20+$ -> 20+
        
        return text.strip()
    
    @staticmethod
    def parse_personal_info(original_text: str) -> Dict[str, str]:
        """解析个人信息"""
        personal_info = {}

        # 提取姓名 - 查找 # 开头的第一行，排除标题行
        lines = original_text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# ') and not any(keyword in line for keyword in ['基本信息', '联系方式', '教育背景', '工作经验', '技能特长', '自我评价', '个人荣誉']):
                name = line[2:].strip()
                if name and len(name) <= 10:  # 姓名通常不会太长
                    personal_info["姓名"] = name
                    break

        # 提取电话 - 更灵活的匹配
        phone_patterns = [
            r'手机[：:\s]*(\d{3}[-\s]*\d{4}[-\s]*\d{4})',
            r'电话[：:\s]*(\d{3}[-\s]*\d{4}[-\s]*\d{4})',
            r'(\d{3}[-\s]*\d{4}[-\s]*\d{4})'
        ]
        for pattern in phone_patterns:
            phone_match = re.search(pattern, original_text)
            if phone_match:
                phone = phone_match.group(1).replace(' ', '').replace('-', '-')
                # 确保格式为 xxx-xxxx-xxxx
                if len(phone.replace('-', '')) == 11:
                    if '-' not in phone:
                        phone = phone[:3] + '-' + phone[3:7] + '-' + phone[7:]
                    personal_info["电话"] = phone
                    break

        # 提取邮箱 - 更宽松的匹配
        email_patterns = [
            r'邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        ]
        for pattern in email_patterns:
            email_match = re.search(pattern, original_text)
            if email_match:
                email = email_match.group(1)
                # 验证邮箱格式
                if '@' in email and '.' in email.split('@')[1]:
                    personal_info["邮箱"] = email
                    break

        return personal_info
    
    @staticmethod
    def parse_time_range(time_str: str) -> tuple:
        """解析时间范围"""
        if not time_str:
            return ("", "")

        if '至今' in time_str:
            start_match = re.search(r'(\d{4}\.\d{2})', time_str)
            return (start_match.group(1) if start_match else "", "至今")

        # 尝试匹配带连字符的格式
        time_match = re.search(r'(\d{4}\.\d{2})\s*[-–]\s*(\d{4}\.\d{2})', time_str)
        if time_match:
            return (time_match.group(1), time_match.group(2))

        # 尝试匹配没有连字符的格式（如：2019.07至今 或 2019.07-至今）
        time_match = re.search(r'(\d{4}\.\d{2})[-–]?至今', time_str)
        if time_match:
            return (time_match.group(1), "至今")

        return ("", "")

    @staticmethod
    def parse_list_items(content: str) -> List[str]:
        """解析列表项"""
        if not content:
            return []
        
        # 清理特殊符号
        cleaned_content = ResumeParser.clean_special_symbols(content)
        
        # 在列表符号前添加换行符
        content_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', cleaned_content)
        
        # 按换行符分割并清理
        items = []
        for line in content_with_breaks.split('\n'):
            line = line.strip()
            if line:
                # 移除列表符号
                line = re.sub(r'^[•●·]\s*', '', line)
                line = line.strip()
                if line:
                    items.append(line)
        
        return items

    @staticmethod
    def parse_work_experience(original_text: str) -> List[Dict[str, Any]]:
        """解析工作经历 - 修复版本"""
        work_experiences = []

        # 1. 首先定位工作经验部分 - 使用更宽松的匹配
        work_section_match = re.search(r'# 工作经验\s*\n([\s\S]*?)(?=\n# 技能特长|\n# 自我评价|$)', original_text)
        if not work_section_match:
            return work_experiences

        work_section = work_section_match.group(1)

        # 2. 解析工作经历 - 重新设计逻辑
        # 按行处理，识别时间、公司、职位的模式
        lines = work_section.split('\n')
        current_entry = {}
        content_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 检查是否是时间行（以#开头且包含时间）
            if line.startswith('# ') and re.search(r'\d{4}\.\d{2}', line):
                # 保存之前的条目
                if current_entry.get("公司") and current_entry.get("职位"):
                    if content_lines:
                        current_entry["工作内容"] = ResumeParser.parse_list_items('\n'.join(content_lines))
                    work_experiences.append({
                        "公司": current_entry["公司"],
                        "职位": current_entry["职位"],
                        "开始时间": current_entry.get("开始时间", ""),
                        "结束时间": current_entry.get("结束时间", ""),
                        "工作内容": current_entry.get("工作内容", [])
                    })

                # 开始新条目
                current_entry = {}
                content_lines = []

                # 解析时间 - 更宽松的匹配
                time_str = line[2:].strip()
                # 直接调用 parse_time_range，它会处理各种格式
                start_date, end_date = ResumeParser.parse_time_range(time_str)
                if start_date or end_date:
                    current_entry["开始时间"] = start_date
                    current_entry["结束时间"] = end_date

                # 查找下一行的公司信息
                j = i + 1
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue
                    if next_line.startswith('# '):
                        company = next_line[2:].strip()
                        current_entry["公司"] = company
                        j += 1

                        # 查找再下一行的职位信息
                        while j < len(lines):
                            next_next_line = lines[j].strip()
                            if not next_next_line:
                                j += 1
                                continue
                            if next_next_line.startswith('# '):
                                position = next_next_line[2:].strip()
                                current_entry["职位"] = position
                                j += 1
                                break
                            else:
                                break
                        break
                    else:
                        break
                i = j - 1  # 调整索引，因为循环末尾会 i += 1

            # 检查是否是独立的时间行（不以#开头）
            elif re.search(r'\d{4}\.\d{2}\s*[-–]\s*\d{4}\.\d{2}', line):
                # 保存之前的条目
                if current_entry.get("公司") and current_entry.get("职位"):
                    if content_lines:
                        current_entry["工作内容"] = ResumeParser.parse_list_items('\n'.join(content_lines))
                    work_experiences.append({
                        "公司": current_entry["公司"],
                        "职位": current_entry["职位"],
                        "开始时间": current_entry.get("开始时间", ""),
                        "结束时间": current_entry.get("结束时间", ""),
                        "工作内容": current_entry.get("工作内容", [])
                    })

                # 开始新条目
                current_entry = {}
                content_lines = []

                # 解析时间
                start_date, end_date = ResumeParser.parse_time_range(line)
                if start_date or end_date:
                    current_entry["开始时间"] = start_date
                    current_entry["结束时间"] = end_date

                # 查找下一行的公司信息
                j = i + 1
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue
                    if next_line.startswith('# '):
                        company = next_line[2:].strip()
                        current_entry["公司"] = company
                        j += 1

                        # 查找再下一行的职位信息
                        while j < len(lines):
                            next_next_line = lines[j].strip()
                            if not next_next_line:
                                j += 1
                                continue
                            if next_next_line.startswith('# '):
                                position = next_next_line[2:].strip()
                                current_entry["职位"] = position
                                j += 1
                                break
                            else:
                                break
                        break
                    else:
                        break
                i = j - 1  # 调整索引，因为循环末尾会 i += 1

            # 其他内容作为工作内容
            else:
                if current_entry:
                    content_lines.append(line)

            i += 1

        # 处理最后一个条目
        if current_entry.get("公司") and current_entry.get("职位"):
            if content_lines:
                current_entry["工作内容"] = ResumeParser.parse_list_items('\n'.join(content_lines))
            work_experiences.append({
                "公司": current_entry["公司"],
                "职位": current_entry["职位"],
                "开始时间": current_entry.get("开始时间", ""),
                "结束时间": current_entry.get("结束时间", ""),
                "工作内容": current_entry.get("工作内容", [])
            })

        return work_experiences
    
    @staticmethod
    def parse_project_experience(original_text: str) -> List[Dict[str, Any]]:
        """解析项目经验"""
        projects = []
        
        # 查找项目部分
        project_sections = re.findall(r'# ([^#\n]*?(?:系统|平台|项目))\s*\n([\s\S]*?)(?=# [^#项]|$)', original_text)
        
        for project_name, content in project_sections:
            project_name = project_name.strip()
            
            # 跳过非项目的部分
            if any(keyword in project_name for keyword in ['工作经历', '教育背景', '专业技能', '基本信息']):
                continue
            
            # 提取项目信息并清理特殊符号
            background_match = re.search(r'# 项目背景[：:]\s*\n([^#]+)', content)
            background = ResumeParser.clean_special_symbols(background_match.group(1)) if background_match else ""
            
            responsibilities_match = re.search(r'# 项目职责[：:]\s*\n([^#]+)', content)
            responsibilities_text = ResumeParser.clean_special_symbols(responsibilities_match.group(1)) if responsibilities_match else ""
            
            achievements_match = re.search(r'# 项目成果[：:]\s*\n([^#]+)', content)
            achievements_text = ResumeParser.clean_special_symbols(achievements_match.group(1)) if achievements_match else ""
            
            # 分割职责和成果为列表
            responsibilities = []
            if responsibilities_text:
                # 处理列表项
                responsibilities_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', responsibilities_text)
                items = responsibilities_with_breaks.split('\n')
                for item in items:
                    item = item.strip()
                    if item:
                        item = re.sub(r'^[•●·]\s*', '', item)
                        item = item.strip()
                        if item:
                            responsibilities.append(item)
            
            achievements = []
            if achievements_text:
                # 处理列表项
                achievements_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', achievements_text)
                items = achievements_with_breaks.split('\n')
                for item in items:
                    item = item.strip()
                    if item:
                        item = re.sub(r'^[•●·]\s*', '', item)
                        item = item.strip()
                        if item:
                            achievements.append(item)
            
            # 提取角色和公司信息
            role = ""
            company = ""
            start_date = ""
            end_date = ""
            
            # 查找角色信息
            role_match = re.search(r'(项目负责人|核心项目经理|技术负责人|产品经理)', content)
            if role_match:
                role = role_match.group(1)
            
            # 查找公司信息
            company_match = re.search(r'(阿里巴巴|腾讯|百度|字节跳动|美团|滴滴)', content)
            if company_match:
                company = company_match.group(1)
                if "阿里巴巴" in company:
                    company = "阿里巴巴集团"
                elif "腾讯" in company:
                    company = "腾讯科技"
            
            # 查找时间信息
            date_match = re.search(r'(\d{4}\.\d{2})-(\d{4}\.\d{2})', content)
            if date_match:
                start_date = date_match.group(1)
                end_date = date_match.group(2)
            
            if background or responsibilities or achievements:
                projects.append({
                    "项目名称": project_name,
                    "角色": role,
                    "公司": company,
                    "开始时间": start_date,
                    "结束时间": end_date,
                    "项目背景": background,
                    "项目职责": responsibilities,
                    "项目成果": achievements
                })
        
        return projects
    
    @staticmethod
    def parse_education(original_text: str) -> List[Dict[str, str]]:
        """解析教育背景 - 修复版本"""
        education = []

        # 1. 定位教育背景部分 - 使用更宽松的匹配
        edu_section_match = re.search(r'# 教育背景\s*\n([\s\S]*?)(?=\n# 工作经验|\n# 技能特长|$)', original_text)
        if not edu_section_match:
            return education

        edu_section = edu_section_match.group(1)

        # 2. 按行处理教育经历，类似工作经历的处理方式
        lines = edu_section.split('\n')
        current_entry = {}
        content_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 检查是否是时间行
            time_match = re.search(r'(\d{4}\.\d{2}\s*[-–]\s*\d{4}\.\d{2})', line)
            if time_match:
                # 保存之前的条目
                if current_entry.get("专业") or current_entry.get("学历"):
                    if content_lines:
                        current_entry["详细信息"] = ResumeParser.parse_list_items('\n'.join(content_lines))
                    education.append({
                        "学校": current_entry.get("学校", "学校名称"),
                        "学历": current_entry.get("学历", ""),
                        "专业": current_entry.get("专业", ""),
                        "开始时间": current_entry.get("开始时间", ""),
                        "结束时间": current_entry.get("结束时间", ""),
                        "详细信息": current_entry.get("详细信息", [])
                    })

                # 开始新条目
                current_entry = {}
                content_lines = []

                start_date, end_date = ResumeParser.parse_time_range(time_match.group(1))
                current_entry["开始时间"] = start_date
                current_entry["结束时间"] = end_date

                # 查找下一行的专业/学历信息
                j = i + 1
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue
                    if next_line.startswith('# '):
                        degree_info = next_line[2:].strip()
                        # 解析专业/学历格式：计算机科学与技术／本科
                        if '／' in degree_info or '/' in degree_info:
                            parts = re.split('[／/]', degree_info)
                            if len(parts) >= 2:
                                current_entry["专业"] = parts[0].strip()
                                current_entry["学历"] = parts[1].strip()
                        else:
                            # 如果没有分隔符，尝试识别学历关键词
                            if any(keyword in degree_info for keyword in ['本科', '硕士', '博士', '学士', '研究生']):
                                current_entry["学历"] = degree_info
                            else:
                                current_entry["专业"] = degree_info
                        j += 1
                        break
                    else:
                        break
                i = j - 1  # 调整索引

            # 检查是否是专业/学历信息（以#开头）
            elif line.startswith('# '):
                degree_info = line[2:].strip()
                # 解析专业/学历格式：计算机科学与技术／本科
                if '／' in degree_info or '/' in degree_info:
                    parts = re.split('[／/]', degree_info)
                    if len(parts) >= 2:
                        current_entry["专业"] = parts[0].strip()
                        current_entry["学历"] = parts[1].strip()
                else:
                    # 如果没有分隔符，尝试识别学历关键词
                    if any(keyword in degree_info for keyword in ['本科', '硕士', '博士', '学士', '研究生']):
                        current_entry["学历"] = degree_info
                    else:
                        current_entry["专业"] = degree_info

            # 其他内容作为详细信息
            else:
                if current_entry:
                    content_lines.append(line)

            i += 1

        # 处理最后一个条目
        if current_entry.get("专业") or current_entry.get("学历"):
            if content_lines:
                current_entry["详细信息"] = ResumeParser.parse_list_items('\n'.join(content_lines))
            education.append({
                "学校": current_entry.get("学校", "学校名称"),
                "学历": current_entry.get("学历", ""),
                "专业": current_entry.get("专业", ""),
                "开始时间": current_entry.get("开始时间", ""),
                "结束时间": current_entry.get("结束时间", ""),
                "详细信息": current_entry.get("详细信息", [])
            })

        return education
    
    @staticmethod
    def parse_skills(original_text: str) -> List[str]:
        """解析技能"""
        skills = []

        # 查找技能特长部分
        skill_section_match = re.search(r'# 技能特长\s*\n([\s\S]*?)(?=\n# [^#]|$)', original_text)
        if skill_section_match:
            skill_section = skill_section_match.group(1)
            # 提取技能关键词
            skill_text = skill_section.strip()
            # 按空格分割技能
            potential_skills = re.split(r'\s+', skill_text)
            for skill in potential_skills:
                skill = skill.strip()
                if skill and len(skill) > 1:  # 过滤掉单字符
                    skills.append(skill)

        # 如果没有找到技能部分，使用默认技能
        if not skills:
            skills = ["项目管理", "团队领导", "数据分析", "产品规划"]

        return skills
    
    @staticmethod
    def parse_resume(original_text: str) -> Dict[str, Any]:
        """解析完整简历"""
        if not original_text:
            return {
                "基本信息": {},
                "工作经历": [],
                "项目经历": [],
                "教育经历": [],
                "技能": []
            }
        
        # 解析各个部分
        personal_info = ResumeParser.parse_personal_info(original_text)
        work_experience = ResumeParser.parse_work_experience(original_text)
        project_experience = ResumeParser.parse_project_experience(original_text)
        education = ResumeParser.parse_education(original_text)
        skills = ResumeParser.parse_skills(original_text)
        
        return {
            "基本信息": personal_info,
            "工作经历": work_experience,
            "项目经历": project_experience,
            "教育经历": education,
            "技能": skills
        }
