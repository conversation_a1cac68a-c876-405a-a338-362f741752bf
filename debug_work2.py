#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'resume-ai-backend'))

import re

# 测试文本
test_text = """# 工作经验

# 2019.07-至今

# 阿里巴巴集团

# 高级产品经理

•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率 $1 5 \%$ •带领5人产品团队，协调研发、设计、运营等多部门资
源，确保项目按时高质量交付•通过数据分析优化产品策略，年度GMV增长达 $3 0 \%$
•建了产品标准化流程，提高团队效率 $2 0 \%$

2017.07 - 2019.06

# 腾讯科技有限公司

# 产品经理

•负责微信小程序生态产品规划，参与设计小程序开放平台功能•主导了2个B端工具类小程序开发，累计服务企业用户10万+• 通过用户调研和数据 
分析，优化产品体验，NPS提升25分•协助制定小程序商业化策略，实现年收入增长 $5 0 \%$

# 技能特长

产品规划 需求分析 用户体验设计 数据分析 项目管理 Axure SQL Python

英语流利"""

def parse_time_range(time_str: str) -> tuple:
    """解析时间范围"""
    if not time_str:
        return ("", "")
    
    if '至今' in time_str:
        start_match = re.search(r'(\d{4}\.\d{2})', time_str)
        return (start_match.group(1) if start_match else "", "至今")
    
    # 尝试匹配带连字符的格式
    time_match = re.search(r'(\d{4}\.\d{2})\s*[-–]\s*(\d{4}\.\d{2})', time_str)
    if time_match:
        return (time_match.group(1), time_match.group(2))
    
    # 尝试匹配没有连字符的格式（如：2019.07至今 或 2019.07-至今）
    time_match = re.search(r'(\d{4}\.\d{2})[-–]?至今', time_str)
    if time_match:
        return (time_match.group(1), "至今")
    
    return ("", "")

def parse_list_items(content: str) -> list:
    """解析列表项"""
    if not content:
        return []
    
    # 简单的列表解析
    items = []
    for line in content.split('\n'):
        line = line.strip()
        if line:
            # 移除列表符号
            line = re.sub(r'^[•●·]\s*', '', line)
            line = line.strip()
            if line:
                items.append(line)
    
    return items

def debug_work_parsing():
    print("🔍 调试工作经历解析（完整模拟）...")
    print("=" * 50)
    
    work_experiences = []
    
    # 1. 定位工作经验部分
    work_section_match = re.search(r'# 工作经验\s*\n([\s\S]*?)(?=\n# 技能特长|\n# 自我评价|$)', test_text)
    if not work_section_match:
        print("❌ 未找到工作经验部分")
        return
    
    work_section = work_section_match.group(1)
    print(f"✅ 找到工作经验部分，长度: {len(work_section)}")
    
    # 2. 解析工作经历
    lines = work_section.split('\n')
    current_entry = {}
    content_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue
        
        print(f"处理第{i}行: '{line}'")
        
        # 检查是否是时间行（以#开头且包含时间）
        if line.startswith('# ') and re.search(r'\d{4}\.\d{2}', line):
            print(f"  -> 识别为时间行")
            # 保存之前的条目
            if current_entry.get("公司") and current_entry.get("职位"):
                print(f"  -> 保存之前的条目: {current_entry}")
                if content_lines:
                    current_entry["工作内容"] = parse_list_items('\n'.join(content_lines))
                work_experiences.append({
                    "公司": current_entry["公司"],
                    "职位": current_entry["职位"],
                    "开始时间": current_entry.get("开始时间", ""),
                    "结束时间": current_entry.get("结束时间", ""),
                    "工作内容": current_entry.get("工作内容", [])
                })
            
            # 开始新条目
            current_entry = {}
            content_lines = []
            
            # 解析时间
            time_str = line[2:].strip()
            start_date, end_date = parse_time_range(time_str)
            if start_date or end_date:
                current_entry["开始时间"] = start_date
                current_entry["结束时间"] = end_date
                print(f"  -> 解析时间: {start_date} - {end_date}")
            
            # 查找下一行的公司信息
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line.startswith('# '):
                    company = next_line[2:].strip()
                    current_entry["公司"] = company
                    print(f"  -> 设置公司: {company}")
                    i += 1
                    
                    # 查找再下一行的职位信息
                    if i + 1 < len(lines):
                        next_next_line = lines[i + 1].strip()
                        if next_next_line.startswith('# '):
                            position = next_next_line[2:].strip()
                            current_entry["职位"] = position
                            print(f"  -> 设置职位: {position}")
                            i += 1
        
        # 检查是否是独立的时间行（不以#开头）
        elif re.search(r'\d{4}\.\d{2}\s*[-–]\s*\d{4}\.\d{2}', line):
            print(f"  -> 识别为独立时间行")
            # 保存之前的条目
            if current_entry.get("公司") and current_entry.get("职位"):
                print(f"  -> 保存之前的条目: {current_entry}")
                if content_lines:
                    current_entry["工作内容"] = parse_list_items('\n'.join(content_lines))
                work_experiences.append({
                    "公司": current_entry["公司"],
                    "职位": current_entry["职位"],
                    "开始时间": current_entry.get("开始时间", ""),
                    "结束时间": current_entry.get("结束时间", ""),
                    "工作内容": current_entry.get("工作内容", [])
                })
            
            # 开始新条目
            current_entry = {}
            content_lines = []
            
            # 解析时间
            start_date, end_date = parse_time_range(line)
            if start_date or end_date:
                current_entry["开始时间"] = start_date
                current_entry["结束时间"] = end_date
                print(f"  -> 解析时间: {start_date} - {end_date}")
            
            # 查找下一行的公司信息
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line.startswith('# '):
                    company = next_line[2:].strip()
                    current_entry["公司"] = company
                    print(f"  -> 设置公司: {company}")
                    i += 1
                    
                    # 查找再下一行的职位信息
                    if i + 1 < len(lines):
                        next_next_line = lines[i + 1].strip()
                        if next_next_line.startswith('# '):
                            position = next_next_line[2:].strip()
                            current_entry["职位"] = position
                            print(f"  -> 设置职位: {position}")
                            i += 1
        
        # 其他内容作为工作内容
        else:
            if current_entry:
                print(f"  -> 识别为内容行")
                content_lines.append(line)
        
        i += 1
    
    # 处理最后一个条目
    print(f"\n处理最后一个条目:")
    print(f"current_entry: {current_entry}")
    print(f"content_lines: {len(content_lines)} 行")
    
    if current_entry.get("公司") and current_entry.get("职位"):
        print(f"  -> 保存最后一个条目")
        if content_lines:
            current_entry["工作内容"] = parse_list_items('\n'.join(content_lines))
        work_experiences.append({
            "公司": current_entry["公司"],
            "职位": current_entry["职位"],
            "开始时间": current_entry.get("开始时间", ""),
            "结束时间": current_entry.get("结束时间", ""),
            "工作内容": current_entry.get("工作内容", [])
        })
    else:
        print(f"  -> 最后一个条目不完整，跳过")
    
    print(f"\n最终结果: {len(work_experiences)} 个工作经历")
    for i, exp in enumerate(work_experiences):
        print(f"工作经历 {i+1}: {exp}")

if __name__ == "__main__":
    debug_work_parsing()
