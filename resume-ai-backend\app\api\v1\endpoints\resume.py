"""
简历相关API端点
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_user
from app.models.user import User
from app.schemas.resume import (
    Resume,
    ResumeCreate,
    ResumeUpdate,
    ResumeUpload,
    ResumeParseRequest,
    ResumeParseResponse,
    ResumeList,
    ResumeVersionCreate,
    ResumeVersion
)
from app.services.resume_service import ResumeService
from app.utils.file_handler import FileHandler

router = APIRouter()



@router.post("/upload", response_model=ResumeUpload)
async def upload_resume(
    file: UploadFile = File(...),
    title: str = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """上传简历文件"""
    # 验证文件名
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件名不能为空"
        )

    # 验证文件类型
    allowed_types = ['.pdf', '.doc', '.docx']
    file_ext = '.' + file.filename.split('.')[-1].lower() if '.' in file.filename else ''

    if file_ext not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_ext}，支持的类型: {allowed_types}"
        )

    # 验证文件大小
    max_size = 10 * 1024 * 1024  # 10MB
    if hasattr(file, 'size') and file.size and file.size > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件过大，最大支持 {max_size // 1024 // 1024}MB"
        )
    resume_service = ResumeService()

    try:
        resume = await resume_service.upload_resume(
            db=db,
            file=file,
            user_id=current_user.id,
            title=title
        )

        # 提取文本内容
        original_text = resume.original_text or ""
        if not original_text:
            try:
                from app.utils.file_handler import FileHandler
                extracted_text = FileHandler.extract_text_from_file(resume.file_path, resume.file_type)
                if extracted_text:
                    original_text = extracted_text
                    resume.original_text = extracted_text
                    db.commit()
            except Exception:
                pass  # 文本提取失败不影响上传

        response_data = {
            "id": resume.id,
            "title": resume.title,
            "original_filename": resume.original_filename,
            "file_type": resume.file_type,
            "file_size": resume.file_size,
            "is_parsed": resume.is_parsed,
            "created_at": resume.created_at.isoformat() if resume.created_at else None,
            "original_text": original_text
        }
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.post("/parse-text", response_model=ResumeParseResponse)
async def parse_resume_text(
    parse_request: ResumeParseRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """解析简历文本"""
    resume_service = ResumeService()
    
    result = await resume_service.parse_resume_text(
        db=db,
        resume_text=parse_request.resume_text,
        user_id=current_user.id
    )
    
    return ResumeParseResponse(**result)


@router.post("/{resume_id}/parse", response_model=Resume)
async def parse_resume(
    resume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """解析简历文件"""
    resume_service = ResumeService()
    
    try:
        resume = await resume_service.parse_resume(
            db=db,
            resume_id=resume_id,
            user_id=current_user.id
        )
        return resume
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="简历解析失败"
        )


@router.post("/", response_model=Resume)
def create_resume(
    resume_create: ResumeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """创建简历"""
    resume = ResumeService.create_resume(
        db=db,
        resume_create=resume_create,
        user_id=current_user.id
    )
    return resume


@router.get("/", response_model=ResumeList)
def get_user_resumes(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取用户简历列表"""
    resumes = ResumeService.get_user_resumes(
        db=db,
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )

    total = len(resumes)  # 简化版本，实际应该查询总数

    return ResumeList(total=total, items=resumes)


@router.get("/{resume_id}", response_model=Resume)
async def get_resume(
    resume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取简历详情"""
    resume = ResumeService.get_resume_by_id(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id
    )

    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="简历不存在"
        )

    # 检查是否需要重新解析
    need_reparse = False

    if not resume.is_parsed:
        need_reparse = True
        print(f"🔍 获取简历DEBUG: 简历 {resume_id} 未解析")
    elif resume.original_text and "PDF解析出错" in resume.original_text:
        need_reparse = True
        print(f"🔍 获取简历DEBUG: 简历 {resume_id} PDF解析出错，需要重新解析")
    elif not resume.structured_data or not resume.structured_data.get('基本信息'):
        need_reparse = True
        print(f"🔍 获取简历DEBUG: 简历 {resume_id} 结构化数据为空，需要重新解析")

    if need_reparse:
        print(f"🔍 获取简历DEBUG: 开始解析简历 {resume_id}...")
        try:
            await ResumeService()._parse_and_structure_resume(db, resume)
            print(f"🔍 获取简历DEBUG: 简历 {resume_id} 解析完成")
        except Exception as e:
            print(f"🔍 获取简历DEBUG: 简历 {resume_id} 解析失败: {e}")
            # 解析失败不影响获取，只是没有结构化数据
    print(f"🔍 返回简历信息: {dir(resume)} ")
    return resume


@router.put("/{resume_id}", response_model=Resume)
def update_resume(
    resume_id: int,
    resume_update: ResumeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """更新简历"""
    resume = ResumeService.update_resume(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id,
        resume_update=resume_update
    )
    
    return resume


@router.delete("/{resume_id}")
def delete_resume(
    resume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """删除简历"""
    success = ResumeService.delete_resume(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id
    )
    
    if success:
        return {"message": "简历删除成功"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="简历删除失败"
        )


@router.post("/{resume_id}/versions", response_model=Resume)
def create_resume_version(
    resume_id: int,
    version_create: ResumeVersionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """创建简历版本"""
    new_version = ResumeService.create_resume_version(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id,
        title=version_create.title,
        structured_data=version_create.structured_data
    )
    
    return new_version


@router.get("/{resume_id}/versions", response_model=List[ResumeVersion])
def get_resume_versions(
    resume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取简历版本列表"""
    versions = ResumeService.get_resume_versions(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id
    )

    return [
        ResumeVersion(
            id=version.id,
            title=version.title,
            version=version.version,
            is_current=version.is_current,
            created_at=version.created_at
        )
        for version in versions
    ]


@router.post("/create_blank/", response_model=Resume)
def create_blank_resume(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """创建空白简历"""
    from datetime import datetime

    # 创建空白简历数据
    blank_resume_data = ResumeCreate(
        title=f"空白简历_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        structured_data={
            "name": "",
            "email": "",
            "phone": "",
            "location": "",
            "summary": "",
            "experience": [],
            "skills": [],
            "education": [],
            "projects": []
        }
    )

    resume = ResumeService.create_resume(
        db=db,
        resume_create=blank_resume_data,
        user_id=current_user.id
    )

    return resume


@router.get("/{resume_id}/preview/", response_model=Resume)
async def get_resume_preview(
    resume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取简历预览数据"""
    resume = ResumeService.get_resume_by_id(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id
    )

    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="简历不存在"
        )

    # 如果简历还没有解析，则触发解析
    if not resume.is_parsed:
        print(f"🔍 预览DEBUG: 简历 {resume_id} 未解析，开始解析...")
        try:
            await ResumeService()._parse_and_structure_resume(db, resume)
            print(f"🔍 预览DEBUG: 简历 {resume_id} 解析完成")
        except Exception as e:
            print(f"🔍 预览DEBUG: 简历 {resume_id} 解析失败: {e}")
            # 解析失败不影响预览，只是没有结构化数据

    return resume


@router.post("/{resume_id}/export/")
async def export_resume(
    resume_id: int,
    export_request: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """导出简历"""
    from fastapi.responses import FileResponse
    import tempfile
    import json

    # 获取简历数据
    resume = ResumeService.get_resume_by_id(
        db=db,
        resume_id=resume_id,
        user_id=current_user.id
    )

    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="简历不存在"
        )

    format_type = export_request.get("format", "pdf")

    try:
        if format_type == "pdf":
            # 这里应该调用PDF生成服务
            # 暂时返回一个临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
            temp_file.write(b"PDF content placeholder")
            temp_file.close()

            return FileResponse(
                path=temp_file.name,
                filename=f"{resume.title}.pdf",
                media_type="application/pdf"
            )
        else:
            # Word格式导出
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".docx")
            temp_file.write(b"Word content placeholder")
            temp_file.close()

            return FileResponse(
                path=temp_file.name,
                filename=f"{resume.title}.docx",
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出失败: {str(e)}"
        )
