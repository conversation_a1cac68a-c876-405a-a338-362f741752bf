{"pdf_info": [{"preproc_blocks": [{"type": "image", "bbox": [259, 39, 338, 119], "blocks": [{"type": "image_body", "bbox": [259, 39, 338, 119], "group_id": 0, "lines": [{"bbox": [259, 39, 338, 119], "spans": [{"bbox": [259, 39, 338, 119], "score": 0.837, "type": "image", "image_path": "06b2b5e2e17444741675d100b26346911cda859b8b765fb1623fc033e0ef6eaf.jpg"}]}], "index": 0.5, "virtual_lines": [{"bbox": [259, 39, 338, 79.0], "spans": [], "index": 0}, {"bbox": [259, 79.0, 338, 119.0], "spans": [], "index": 1}]}], "index": 0.5}, {"type": "title", "bbox": [281, 146, 315, 164], "lines": [{"bbox": [279, 143, 317, 167], "spans": [{"bbox": [279, 143, 317, 167], "score": 0.999, "content": "张三", "type": "text"}], "index": 2}], "index": 2}, {"type": "title", "bbox": [45, 188, 90, 202], "lines": [{"bbox": [43, 186, 92, 204], "spans": [{"bbox": [43, 186, 92, 204], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 3}], "index": 3}, {"type": "text", "bbox": [48, 216, 119, 366], "lines": [{"bbox": [49, 217, 72, 232], "spans": [{"bbox": [49, 217, 72, 232], "score": 0.996, "content": "生日", "type": "text"}], "index": 4}, {"bbox": [49, 234, 118, 247], "spans": [{"bbox": [49, 234, 118, 247], "score": 1.0, "content": "1990年5月15日", "type": "text"}], "index": 5}, {"bbox": [48, 256, 72, 270], "spans": [{"bbox": [48, 256, 72, 270], "score": 0.999, "content": "籍贯", "type": "text"}], "index": 6}, {"bbox": [49, 272, 92, 286], "spans": [{"bbox": [49, 272, 92, 286], "score": 1.0, "content": "江苏南京", "type": "text"}], "index": 7}, {"bbox": [49, 296, 82, 309], "spans": [{"bbox": [49, 296, 82, 309], "score": 1.0, "content": "现居地", "type": "text"}], "index": 8}, {"bbox": [49, 311, 73, 326], "spans": [{"bbox": [49, 311, 73, 326], "score": 1.0, "content": "上海", "type": "text"}], "index": 9}, {"bbox": [49, 335, 92, 349], "spans": [{"bbox": [49, 335, 92, 349], "score": 1.0, "content": "求职意向", "type": "text"}], "index": 10}, {"bbox": [50, 351, 111, 364], "spans": [{"bbox": [50, 351, 111, 364], "score": 1.0, "content": "高级产品经理", "type": "text"}], "index": 11}], "index": 7.5}, {"type": "title", "bbox": [45, 396, 90, 409], "lines": [{"bbox": [43, 394, 92, 411], "spans": [{"bbox": [43, 394, 92, 411], "score": 0.999, "content": "联系方式", "type": "text"}], "index": 12}], "index": 12}, {"type": "text", "bbox": [48, 424, 167, 494], "lines": [{"bbox": [48, 424, 74, 439], "spans": [{"bbox": [48, 424, 74, 439], "score": 1.0, "content": "手机", "type": "text"}], "index": 13}, {"bbox": [49, 441, 119, 453], "spans": [{"bbox": [49, 441, 119, 453], "score": 0.999, "content": "138-1234-5678", "type": "text"}], "index": 14}, {"bbox": [48, 462, 73, 478], "spans": [{"bbox": [48, 462, 73, 478], "score": 1.0, "content": "邮箱", "type": "text"}], "index": 15}, {"bbox": [50, 480, 165, 494], "spans": [{"bbox": [50, 480, 165, 494], "score": 0.999, "content": "<EMAIL>", "type": "text"}], "index": 16}], "index": 14.5}, {"type": "title", "bbox": [45, 524, 90, 538], "lines": [{"bbox": [43, 522, 92, 541], "spans": [{"bbox": [43, 522, 92, 541], "score": 0.998, "content": "个人荣誉", "type": "text"}], "index": 17}], "index": 17}, {"type": "text", "bbox": [52, 553, 153, 614], "lines": [{"bbox": [51, 554, 145, 572], "spans": [{"bbox": [51, 554, 145, 572], "score": 0.981, "content": "•2020年度优秀员工", "type": "text"}], "index": 18}, {"bbox": [51, 577, 146, 595], "spans": [{"bbox": [51, 577, 146, 595], "score": 0.988, "content": "•2019年创新项目奖", "type": "text"}], "index": 19}, {"bbox": [52, 601, 154, 616], "spans": [{"bbox": [52, 601, 154, 616], "score": 0.943, "content": "•2018年行业最佳新人", "type": "text"}], "index": 20}], "index": 19}, {"type": "title", "bbox": [66, 671, 123, 687], "lines": [{"bbox": [65, 670, 124, 689], "spans": [{"bbox": [65, 670, 124, 689], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 21}], "index": 21}, {"type": "text", "bbox": [56, 711, 158, 725], "lines": [{"bbox": [55, 710, 158, 726], "spans": [{"bbox": [55, 710, 158, 726], "score": 0.962, "content": "2010.09 - 2014.06", "type": "text"}], "index": 22}], "index": 22}, {"type": "title", "bbox": [57, 733, 189, 749], "lines": [{"bbox": [55, 733, 188, 749], "spans": [{"bbox": [55, 733, 188, 749], "score": 0.978, "content": "计算机科学与技术／本科", "type": "text"}], "index": 23}], "index": 23}, {"type": "text", "bbox": [59, 753, 345, 801], "lines": [{"bbox": [58, 753, 344, 766], "spans": [{"bbox": [58, 753, 344, 766], "score": 0.99, "content": "•主修课程：数据结构、算法设计、数据库原理、计算机网络", "type": "text"}], "index": 24}, {"bbox": [59, 770, 186, 783], "spans": [{"bbox": [59, 770, 169, 783], "score": 0.972, "content": "•GPA:3.8/4.0，排名前", "type": "text"}, {"bbox": [169, 770, 186, 783], "score": 0.85, "content": "5 \\%", "type": "inline_equation"}], "index": 25}, {"bbox": [59, 787, 182, 801], "spans": [{"bbox": [59, 787, 182, 801], "score": 0.982, "content": "•获得校级一等奖学金2次", "type": "text"}], "index": 26}], "index": 25}], "page_idx": 0, "page_size": [595, 841], "discarded_blocks": [{"type": "discarded", "bbox": [23, 14, 82, 24], "lines": [{"bbox": [23, 14, 83, 24], "spans": [{"bbox": [23, 14, 83, 24], "score": 0.988, "content": "2025/7/23 10:44", "type": "text"}]}]}, {"type": "discarded", "bbox": [560, 819, 573, 828], "lines": [{"bbox": [559, 817, 574, 830], "spans": [{"bbox": [559, 817, 574, 830], "score": 0.992, "content": "1/2", "type": "text"}]}]}, {"type": "discarded", "bbox": [24, 817, 234, 828], "lines": [{"bbox": [23, 817, 234, 828], "spans": [{"bbox": [23, 817, 234, 828], "score": 0.971, "content": "file://C:/Users/<USER>/Desktop/Al简历/学生简历.html", "type": "text"}]}]}, {"type": "discarded", "bbox": [325, 14, 359, 25], "lines": [{"bbox": [324, 14, 360, 27], "spans": [{"bbox": [324, 14, 360, 27], "score": 0.999, "content": "专业简历", "type": "text"}]}]}, {"type": "discarded", "bbox": [492, 710, 543, 726], "lines": [{"bbox": [491, 709, 545, 727], "spans": [{"bbox": [491, 709, 545, 727], "score": 1.0, "content": "清华大学", "type": "text"}]}]}, {"type": "discarded", "bbox": [44, 672, 61, 686], "lines": [{"bbox": [47, 678, 53, 684], "spans": [{"bbox": [47, 678, 53, 684], "score": 0.868, "content": "「", "type": "text"}]}]}], "para_blocks": [{"type": "image", "bbox": [259, 39, 338, 119], "blocks": [{"type": "image_body", "bbox": [259, 39, 338, 119], "group_id": 0, "lines": [{"bbox": [259, 39, 338, 119], "spans": [{"bbox": [259, 39, 338, 119], "score": 0.837, "type": "image", "image_path": "06b2b5e2e17444741675d100b26346911cda859b8b765fb1623fc033e0ef6eaf.jpg"}]}], "index": 0.5, "virtual_lines": [{"bbox": [259, 39, 338, 79.0], "spans": [], "index": 0}, {"bbox": [259, 79.0, 338, 119.0], "spans": [], "index": 1}]}], "index": 0.5}, {"type": "title", "bbox": [281, 146, 315, 164], "lines": [{"bbox": [279, 143, 317, 167], "spans": [{"bbox": [279, 143, 317, 167], "score": 0.999, "content": "张三", "type": "text"}], "index": 2}], "index": 2}, {"type": "title", "bbox": [45, 188, 90, 202], "lines": [{"bbox": [43, 186, 92, 204], "spans": [{"bbox": [43, 186, 92, 204], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 3}], "index": 3}, {"type": "list", "bbox": [48, 216, 119, 366], "lines": [{"bbox": [49, 217, 72, 232], "spans": [{"bbox": [49, 217, 72, 232], "score": 0.996, "content": "生日", "type": "text"}], "index": 4, "is_list_start_line": true}, {"bbox": [49, 234, 118, 247], "spans": [{"bbox": [49, 234, 118, 247], "score": 1.0, "content": "1990年5月15日", "type": "text"}], "index": 5, "is_list_start_line": true}, {"bbox": [48, 256, 72, 270], "spans": [{"bbox": [48, 256, 72, 270], "score": 0.999, "content": "籍贯", "type": "text"}], "index": 6, "is_list_start_line": true}, {"bbox": [49, 272, 92, 286], "spans": [{"bbox": [49, 272, 92, 286], "score": 1.0, "content": "江苏南京", "type": "text"}], "index": 7, "is_list_start_line": true}, {"bbox": [49, 296, 82, 309], "spans": [{"bbox": [49, 296, 82, 309], "score": 1.0, "content": "现居地", "type": "text"}], "index": 8, "is_list_start_line": true}, {"bbox": [49, 311, 73, 326], "spans": [{"bbox": [49, 311, 73, 326], "score": 1.0, "content": "上海", "type": "text"}], "index": 9, "is_list_start_line": true}, {"bbox": [49, 335, 92, 349], "spans": [{"bbox": [49, 335, 92, 349], "score": 1.0, "content": "求职意向", "type": "text"}], "index": 10, "is_list_start_line": true}, {"bbox": [50, 351, 111, 364], "spans": [{"bbox": [50, 351, 111, 364], "score": 1.0, "content": "高级产品经理", "type": "text"}], "index": 11, "is_list_start_line": true}], "index": 7.5, "bbox_fs": [48, 217, 118, 364]}, {"type": "title", "bbox": [45, 396, 90, 409], "lines": [{"bbox": [43, 394, 92, 411], "spans": [{"bbox": [43, 394, 92, 411], "score": 0.999, "content": "联系方式", "type": "text"}], "index": 12}], "index": 12}, {"type": "list", "bbox": [48, 424, 167, 494], "lines": [{"bbox": [48, 424, 74, 439], "spans": [{"bbox": [48, 424, 74, 439], "score": 1.0, "content": "手机", "type": "text"}], "index": 13, "is_list_start_line": true}, {"bbox": [49, 441, 119, 453], "spans": [{"bbox": [49, 441, 119, 453], "score": 0.999, "content": "138-1234-5678", "type": "text"}], "index": 14, "is_list_start_line": true}, {"bbox": [48, 462, 73, 478], "spans": [{"bbox": [48, 462, 73, 478], "score": 1.0, "content": "邮箱", "type": "text"}], "index": 15, "is_list_start_line": true}, {"bbox": [50, 480, 165, 494], "spans": [{"bbox": [50, 480, 165, 494], "score": 0.999, "content": "<EMAIL>", "type": "text"}], "index": 16, "is_list_start_line": true}], "index": 14.5, "bbox_fs": [48, 424, 165, 494]}, {"type": "title", "bbox": [45, 524, 90, 538], "lines": [{"bbox": [43, 522, 92, 541], "spans": [{"bbox": [43, 522, 92, 541], "score": 0.998, "content": "个人荣誉", "type": "text"}], "index": 17}], "index": 17}, {"type": "text", "bbox": [52, 553, 153, 614], "lines": [{"bbox": [51, 554, 145, 572], "spans": [{"bbox": [51, 554, 145, 572], "score": 0.981, "content": "•2020年度优秀员工", "type": "text"}], "index": 18}, {"bbox": [51, 577, 146, 595], "spans": [{"bbox": [51, 577, 146, 595], "score": 0.988, "content": "•2019年创新项目奖", "type": "text"}], "index": 19}, {"bbox": [52, 601, 154, 616], "spans": [{"bbox": [52, 601, 154, 616], "score": 0.943, "content": "•2018年行业最佳新人", "type": "text"}], "index": 20}], "index": 19, "bbox_fs": [51, 554, 154, 616]}, {"type": "title", "bbox": [66, 671, 123, 687], "lines": [{"bbox": [65, 670, 124, 689], "spans": [{"bbox": [65, 670, 124, 689], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 21}], "index": 21}, {"type": "text", "bbox": [56, 711, 158, 725], "lines": [{"bbox": [55, 710, 158, 726], "spans": [{"bbox": [55, 710, 158, 726], "score": 0.962, "content": "2010.09 - 2014.06", "type": "text"}], "index": 22}], "index": 22, "bbox_fs": [55, 710, 158, 726]}, {"type": "title", "bbox": [57, 733, 189, 749], "lines": [{"bbox": [55, 733, 188, 749], "spans": [{"bbox": [55, 733, 188, 749], "score": 0.978, "content": "计算机科学与技术／本科", "type": "text"}], "index": 23}], "index": 23}, {"type": "list", "bbox": [59, 753, 345, 801], "lines": [{"bbox": [58, 753, 344, 766], "spans": [{"bbox": [58, 753, 344, 766], "score": 0.99, "content": "•主修课程：数据结构、算法设计、数据库原理、计算机网络", "type": "text"}], "index": 24, "is_list_start_line": true}, {"bbox": [59, 770, 186, 783], "spans": [{"bbox": [59, 770, 169, 783], "score": 0.972, "content": "•GPA:3.8/4.0，排名前", "type": "text"}, {"bbox": [169, 770, 186, 783], "score": 0.85, "content": "5 \\%", "type": "inline_equation"}], "index": 25, "is_list_start_line": true}, {"bbox": [59, 787, 182, 801], "spans": [{"bbox": [59, 787, 182, 801], "score": 0.982, "content": "•获得校级一等奖学金2次", "type": "text"}], "index": 26, "is_list_start_line": true}], "index": 25, "bbox_fs": [58, 753, 344, 801]}]}, {"preproc_blocks": [{"type": "text", "bbox": [56, 38, 158, 52], "lines": [{"bbox": [55, 38, 158, 54], "spans": [{"bbox": [55, 38, 158, 54], "score": 0.961, "content": "2014.09 - 2017.06", "type": "text"}], "index": 0}], "index": 0}, {"type": "title", "bbox": [56, 61, 140, 76], "lines": [{"bbox": [55, 60, 141, 77], "spans": [{"bbox": [55, 60, 141, 77], "score": 0.966, "content": "软件工程/硕士", "type": "text"}], "index": 1}], "index": 1}, {"type": "text", "bbox": [59, 80, 219, 128], "lines": [{"bbox": [59, 80, 218, 94], "spans": [{"bbox": [59, 80, 218, 94], "score": 0.989, "content": "•研究向：智能与机器学习", "type": "text"}], "index": 2}, {"bbox": [59, 97, 218, 110], "spans": [{"bbox": [59, 97, 218, 110], "score": 0.977, "content": "•发表论文2篇，其中1篇被EI收录", "type": "text"}], "index": 3}, {"bbox": [59, 114, 196, 128], "spans": [{"bbox": [59, 114, 196, 128], "score": 0.986, "content": "•参与国家自然科学基金项目", "type": "text"}], "index": 4}], "index": 3}, {"type": "title", "bbox": [67, 154, 123, 171], "lines": [{"bbox": [65, 154, 124, 173], "spans": [{"bbox": [65, 154, 124, 173], "score": 1.0, "content": "工作经验", "type": "text"}], "index": 5}], "index": 5}, {"type": "title", "bbox": [55, 194, 136, 209], "lines": [{"bbox": [54, 192, 137, 211], "spans": [{"bbox": [54, 192, 137, 211], "score": 0.993, "content": "2019.07-至今", "type": "text"}], "index": 6}], "index": 6}, {"type": "title", "bbox": [468, 193, 543, 209], "lines": [{"bbox": [469, 194, 543, 209], "spans": [{"bbox": [469, 194, 543, 209], "score": 0.999, "content": "阿里巴巴集团", "type": "text"}], "index": 7}], "index": 7}, {"type": "title", "bbox": [56, 217, 131, 232], "lines": [{"bbox": [56, 218, 131, 232], "spans": [{"bbox": [56, 218, 131, 232], "score": 1.0, "content": "高级产品经理", "type": "text"}], "index": 8}], "index": 8}, {"type": "text", "bbox": [59, 236, 456, 301], "lines": [{"bbox": [58, 236, 455, 249], "spans": [{"bbox": [58, 236, 432, 249], "score": 0.976, "content": "•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率", "type": "text"}, {"bbox": [433, 236, 455, 249], "score": 0.87, "content": "1 5 \\%", "type": "inline_equation"}], "index": 9}, {"bbox": [57, 252, 455, 267], "spans": [{"bbox": [57, 252, 455, 267], "score": 0.989, "content": "•带领5人产品团队，协调研发、设计、运营等多部门资源，确保项目按时高质量交付", "type": "text"}], "index": 10}, {"bbox": [58, 270, 303, 283], "spans": [{"bbox": [58, 270, 280, 283], "score": 0.978, "content": "•通过数据分析优化产品策略，年度GMV增长达", "type": "text"}, {"bbox": [280, 271, 303, 283], "score": 0.88, "content": "3 0 \\%", "type": "inline_equation"}], "index": 11}, {"bbox": [58, 286, 271, 300], "spans": [{"bbox": [58, 286, 247, 300], "score": 0.98, "content": "•建了产品标准化流程，提高团队效率", "type": "text"}, {"bbox": [248, 287, 271, 300], "score": 0.89, "content": "2 0 \\%", "type": "inline_equation"}], "index": 12}], "index": 10.5}, {"type": "text", "bbox": [56, 363, 158, 378], "lines": [{"bbox": [55, 363, 158, 380], "spans": [{"bbox": [55, 363, 158, 380], "score": 0.948, "content": "2017.07 - 2019.06", "type": "text"}], "index": 13}], "index": 13}, {"type": "title", "bbox": [444, 363, 543, 379], "lines": [{"bbox": [445, 364, 543, 379], "spans": [{"bbox": [445, 364, 543, 379], "score": 0.999, "content": "腾讯科技有限公司", "type": "text"}], "index": 14}], "index": 14}, {"type": "title", "bbox": [56, 387, 106, 401], "lines": [{"bbox": [55, 387, 107, 402], "spans": [{"bbox": [55, 387, 107, 402], "score": 1.0, "content": "产品经理", "type": "text"}], "index": 15}], "index": 15}, {"type": "text", "bbox": [59, 406, 355, 471], "lines": [{"bbox": [58, 406, 354, 419], "spans": [{"bbox": [58, 406, 354, 419], "score": 0.988, "content": "•负责微信小程序生态产品规划，参与设计小程序开放平台功能", "type": "text"}], "index": 16}, {"bbox": [58, 423, 343, 436], "spans": [{"bbox": [58, 423, 343, 436], "score": 0.988, "content": "•主导了2个B端工具类小程序开发，累计服务企业用户10万+", "type": "text"}], "index": 17}, {"bbox": [59, 440, 333, 453], "spans": [{"bbox": [59, 440, 333, 453], "score": 0.979, "content": "• 通过用户调研和数据分析，优化产品体验，NPS提升25分", "type": "text"}], "index": 18}, {"bbox": [59, 457, 302, 470], "spans": [{"bbox": [59, 457, 279, 470], "score": 0.985, "content": "•协助制定小程序商业化策略，实现年收入增长", "type": "text"}, {"bbox": [279, 457, 302, 469], "score": 0.88, "content": "5 0 \\%", "type": "inline_equation"}], "index": 19}], "index": 17.5}, {"type": "title", "bbox": [61, 497, 118, 514], "lines": [{"bbox": [60, 497, 120, 515], "spans": [{"bbox": [60, 497, 120, 515], "score": 1.0, "content": "技能特长", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [62, 537, 519, 553], "lines": [{"bbox": [62, 536, 516, 555], "spans": [{"bbox": [62, 536, 106, 554], "score": 1.0, "content": "产品规划", "type": "text"}, {"bbox": [125, 538, 167, 552], "score": 0.999, "content": "需求分析", "type": "text"}, {"bbox": [186, 538, 247, 552], "score": 1.0, "content": "用户体验设计", "type": "text"}, {"bbox": [267, 537, 310, 552], "score": 1.0, "content": "数据分析", "type": "text"}, {"bbox": [328, 538, 371, 552], "score": 1.0, "content": "项目管理", "type": "text"}, {"bbox": [389, 539, 421, 553], "score": 0.997, "content": "Axure", "type": "text"}, {"bbox": [437, 538, 466, 554], "score": 0.995, "content": "SQL", "type": "text"}, {"bbox": [474, 537, 516, 555], "score": 0.999, "content": "Python", "type": "text"}], "index": 21}], "index": 21}, {"type": "text", "bbox": [63, 568, 105, 581], "lines": [{"bbox": [63, 568, 105, 582], "spans": [{"bbox": [63, 568, 105, 582], "score": 1.0, "content": "英语流利", "type": "text"}], "index": 22}], "index": 22}, {"type": "title", "bbox": [67, 611, 123, 628], "lines": [{"bbox": [66, 610, 124, 631], "spans": [{"bbox": [66, 610, 124, 631], "score": 1.0, "content": "自我评价", "type": "text"}], "index": 23}], "index": 23}, {"type": "text", "bbox": [55, 650, 543, 699], "lines": [{"bbox": [57, 653, 537, 663], "spans": [{"bbox": [57, 653, 537, 663], "score": 0.999, "content": "5年互联网产品经理经验，熟悉从0到1的产品开发全流程。具备敏锐的商业嗅觉和用户洞察力，擅长通过", "type": "text"}], "index": 24}, {"bbox": [56, 668, 542, 681], "spans": [{"bbox": [56, 668, 542, 681], "score": 1.0, "content": "数据驱动产品决策。拥有优秀的跨部门沟通能力和团队管理经验，能够在高压环境下高效工作。持续关注", "type": "text"}], "index": 25}, {"bbox": [56, 685, 325, 698], "spans": [{"bbox": [56, 685, 325, 698], "score": 1.0, "content": "行业动态和技术发展趋势，致力于创造有价值的产品体验。", "type": "text"}], "index": 26}], "index": 25}], "page_idx": 1, "page_size": [595, 841], "discarded_blocks": [{"type": "discarded", "bbox": [24, 817, 234, 828], "lines": [{"bbox": [22, 817, 234, 828], "spans": [{"bbox": [22, 817, 234, 828], "score": 0.97, "content": "file:///C:/Users/<USER>/Desktop/Al简历/学生简历.html", "type": "text"}]}]}, {"type": "discarded", "bbox": [560, 819, 573, 828], "lines": [{"bbox": [558, 817, 574, 830], "spans": [{"bbox": [558, 817, 574, 830], "score": 0.939, "content": "2/2", "type": "text"}]}]}, {"type": "discarded", "bbox": [23, 14, 83, 24], "lines": [{"bbox": [23, 14, 83, 24], "spans": [{"bbox": [23, 14, 83, 24], "score": 0.988, "content": "2025/7/23 10:44", "type": "text"}]}]}, {"type": "discarded", "bbox": [44, 154, 61, 171], "lines": []}, {"type": "discarded", "bbox": [325, 14, 359, 25], "lines": [{"bbox": [324, 14, 360, 26], "spans": [{"bbox": [324, 14, 360, 26], "score": 0.999, "content": "专业简历", "type": "text"}]}]}, {"type": "discarded", "bbox": [470, 37, 543, 53], "lines": [{"bbox": [469, 37, 544, 54], "spans": [{"bbox": [469, 37, 544, 54], "score": 1.0, "content": "上海交通大学", "type": "text"}]}]}, {"type": "discarded", "bbox": [43, 612, 61, 627], "lines": []}, {"type": "discarded", "bbox": [43, 496, 57, 515], "lines": []}], "para_blocks": [{"type": "text", "bbox": [56, 38, 158, 52], "lines": [{"bbox": [55, 38, 158, 54], "spans": [{"bbox": [55, 38, 158, 54], "score": 0.961, "content": "2014.09 - 2017.06", "type": "text"}], "index": 0}], "index": 0, "bbox_fs": [55, 38, 158, 54]}, {"type": "title", "bbox": [56, 61, 140, 76], "lines": [{"bbox": [55, 60, 141, 77], "spans": [{"bbox": [55, 60, 141, 77], "score": 0.966, "content": "软件工程/硕士", "type": "text"}], "index": 1}], "index": 1}, {"type": "text", "bbox": [59, 80, 219, 128], "lines": [{"bbox": [59, 80, 218, 94], "spans": [{"bbox": [59, 80, 218, 94], "score": 0.989, "content": "•研究向：智能与机器学习", "type": "text"}], "index": 2}, {"bbox": [59, 97, 218, 110], "spans": [{"bbox": [59, 97, 218, 110], "score": 0.977, "content": "•发表论文2篇，其中1篇被EI收录", "type": "text"}], "index": 3}, {"bbox": [59, 114, 196, 128], "spans": [{"bbox": [59, 114, 196, 128], "score": 0.986, "content": "•参与国家自然科学基金项目", "type": "text"}], "index": 4}], "index": 3, "bbox_fs": [59, 80, 218, 128]}, {"type": "title", "bbox": [67, 154, 123, 171], "lines": [{"bbox": [65, 154, 124, 173], "spans": [{"bbox": [65, 154, 124, 173], "score": 1.0, "content": "工作经验", "type": "text"}], "index": 5}], "index": 5}, {"type": "title", "bbox": [55, 194, 136, 209], "lines": [{"bbox": [54, 192, 137, 211], "spans": [{"bbox": [54, 192, 137, 211], "score": 0.993, "content": "2019.07-至今", "type": "text"}], "index": 6}], "index": 6}, {"type": "title", "bbox": [468, 193, 543, 209], "lines": [{"bbox": [469, 194, 543, 209], "spans": [{"bbox": [469, 194, 543, 209], "score": 0.999, "content": "阿里巴巴集团", "type": "text"}], "index": 7}], "index": 7}, {"type": "title", "bbox": [56, 217, 131, 232], "lines": [{"bbox": [56, 218, 131, 232], "spans": [{"bbox": [56, 218, 131, 232], "score": 1.0, "content": "高级产品经理", "type": "text"}], "index": 8}], "index": 8}, {"type": "list", "bbox": [59, 236, 456, 301], "lines": [{"bbox": [58, 236, 455, 249], "spans": [{"bbox": [58, 236, 432, 249], "score": 0.976, "content": "•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率", "type": "text"}, {"bbox": [433, 236, 455, 249], "score": 0.87, "content": "1 5 \\%", "type": "inline_equation"}], "index": 9}, {"bbox": [57, 252, 455, 267], "spans": [{"bbox": [57, 252, 455, 267], "score": 0.989, "content": "•带领5人产品团队，协调研发、设计、运营等多部门资源，确保项目按时高质量交付", "type": "text"}], "index": 10}, {"bbox": [58, 270, 303, 283], "spans": [{"bbox": [58, 270, 280, 283], "score": 0.978, "content": "•通过数据分析优化产品策略，年度GMV增长达", "type": "text"}, {"bbox": [280, 271, 303, 283], "score": 0.88, "content": "3 0 \\%", "type": "inline_equation"}], "index": 11, "is_list_end_line": true}, {"bbox": [58, 286, 271, 300], "spans": [{"bbox": [58, 286, 247, 300], "score": 0.98, "content": "•建了产品标准化流程，提高团队效率", "type": "text"}, {"bbox": [248, 287, 271, 300], "score": 0.89, "content": "2 0 \\%", "type": "inline_equation"}], "index": 12, "is_list_start_line": true, "is_list_end_line": true}], "index": 10.5, "bbox_fs": [57, 236, 455, 300]}, {"type": "text", "bbox": [56, 363, 158, 378], "lines": [{"bbox": [55, 363, 158, 380], "spans": [{"bbox": [55, 363, 158, 380], "score": 0.948, "content": "2017.07 - 2019.06", "type": "text"}], "index": 13}], "index": 13, "bbox_fs": [55, 363, 158, 380]}, {"type": "title", "bbox": [444, 363, 543, 379], "lines": [{"bbox": [445, 364, 543, 379], "spans": [{"bbox": [445, 364, 543, 379], "score": 0.999, "content": "腾讯科技有限公司", "type": "text"}], "index": 14}], "index": 14}, {"type": "title", "bbox": [56, 387, 106, 401], "lines": [{"bbox": [55, 387, 107, 402], "spans": [{"bbox": [55, 387, 107, 402], "score": 1.0, "content": "产品经理", "type": "text"}], "index": 15}], "index": 15}, {"type": "text", "bbox": [59, 406, 355, 471], "lines": [{"bbox": [58, 406, 354, 419], "spans": [{"bbox": [58, 406, 354, 419], "score": 0.988, "content": "•负责微信小程序生态产品规划，参与设计小程序开放平台功能", "type": "text"}], "index": 16}, {"bbox": [58, 423, 343, 436], "spans": [{"bbox": [58, 423, 343, 436], "score": 0.988, "content": "•主导了2个B端工具类小程序开发，累计服务企业用户10万+", "type": "text"}], "index": 17}, {"bbox": [59, 440, 333, 453], "spans": [{"bbox": [59, 440, 333, 453], "score": 0.979, "content": "• 通过用户调研和数据分析，优化产品体验，NPS提升25分", "type": "text"}], "index": 18}, {"bbox": [59, 457, 302, 470], "spans": [{"bbox": [59, 457, 279, 470], "score": 0.985, "content": "•协助制定小程序商业化策略，实现年收入增长", "type": "text"}, {"bbox": [279, 457, 302, 469], "score": 0.88, "content": "5 0 \\%", "type": "inline_equation"}], "index": 19}], "index": 17.5, "bbox_fs": [58, 406, 354, 470]}, {"type": "title", "bbox": [61, 497, 118, 514], "lines": [{"bbox": [60, 497, 120, 515], "spans": [{"bbox": [60, 497, 120, 515], "score": 1.0, "content": "技能特长", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [62, 537, 519, 553], "lines": [{"bbox": [62, 536, 516, 555], "spans": [{"bbox": [62, 536, 106, 554], "score": 1.0, "content": "产品规划", "type": "text"}, {"bbox": [125, 538, 167, 552], "score": 0.999, "content": "需求分析", "type": "text"}, {"bbox": [186, 538, 247, 552], "score": 1.0, "content": "用户体验设计", "type": "text"}, {"bbox": [267, 537, 310, 552], "score": 1.0, "content": "数据分析", "type": "text"}, {"bbox": [328, 538, 371, 552], "score": 1.0, "content": "项目管理", "type": "text"}, {"bbox": [389, 539, 421, 553], "score": 0.997, "content": "Axure", "type": "text"}, {"bbox": [437, 538, 466, 554], "score": 0.995, "content": "SQL", "type": "text"}, {"bbox": [474, 537, 516, 555], "score": 0.999, "content": "Python", "type": "text"}], "index": 21}], "index": 21, "bbox_fs": [62, 536, 516, 555]}, {"type": "text", "bbox": [63, 568, 105, 581], "lines": [{"bbox": [63, 568, 105, 582], "spans": [{"bbox": [63, 568, 105, 582], "score": 1.0, "content": "英语流利", "type": "text"}], "index": 22}], "index": 22, "bbox_fs": [63, 568, 105, 582]}, {"type": "title", "bbox": [67, 611, 123, 628], "lines": [{"bbox": [66, 610, 124, 631], "spans": [{"bbox": [66, 610, 124, 631], "score": 1.0, "content": "自我评价", "type": "text"}], "index": 23}], "index": 23}, {"type": "text", "bbox": [55, 650, 543, 699], "lines": [{"bbox": [57, 653, 537, 663], "spans": [{"bbox": [57, 653, 537, 663], "score": 0.999, "content": "5年互联网产品经理经验，熟悉从0到1的产品开发全流程。具备敏锐的商业嗅觉和用户洞察力，擅长通过", "type": "text"}], "index": 24}, {"bbox": [56, 668, 542, 681], "spans": [{"bbox": [56, 668, 542, 681], "score": 1.0, "content": "数据驱动产品决策。拥有优秀的跨部门沟通能力和团队管理经验，能够在高压环境下高效工作。持续关注", "type": "text"}], "index": 25}, {"bbox": [56, 685, 325, 698], "spans": [{"bbox": [56, 685, 325, 698], "score": 1.0, "content": "行业动态和技术发展趋势，致力于创造有价值的产品体验。", "type": "text"}], "index": 26}], "index": 25, "bbox_fs": [56, 653, 542, 698]}]}], "_backend": "pipeline", "_version_name": "2.1.5"}