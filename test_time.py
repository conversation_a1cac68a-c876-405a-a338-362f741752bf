#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'resume-ai-backend'))

from app.utils.resume_parser import <PERSON><PERSON><PERSON>ars<PERSON>

def test_time_parsing():
    test_cases = [
        "2019.07-至今",
        "2017.07 - 2019.06",
        "2019.07至今",
        "2020.01-2021.12"
    ]
    
    for case in test_cases:
        result = ResumeParser.parse_time_range(case)
        print(f"输入: '{case}' -> 输出: {result}")

if __name__ == "__main__":
    test_time_parsing()
