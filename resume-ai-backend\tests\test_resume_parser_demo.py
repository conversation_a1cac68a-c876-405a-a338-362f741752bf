import re
import json
from typing import Dict, List, Any

class ResumeParser:
    def __init__(self):
        pass

    def clean_text(self, text: str) -> str:
        """清理文本，移除多余的空白字符和特殊符号"""
        if not text:
            return ""
        # 移除LaTeX符号和多余空格
        text = re.sub(r'\$[^$]*\$', '', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[•●·]\s*', '', text)  # 移除项目符号
        return text.strip()

    def extract_personal_info(self, resume_text: str) -> Dict[str, Any]:
        """提取个人信息"""
        personal_info = {}
        
        # 姓名（第一行的#标题）
        name_match = re.search(r'^#\s*([^\n#]+)', resume_text, re.MULTILINE)
        if name_match:
            personal_info['name'] = self.clean_text(name_match.group(1))
        
        # 手机号
        phone_matches = re.findall(r'(?:手机|电话)?\s*(\d{3}-?\d{4}-?\d{4})', resume_text)
        if phone_matches:
            personal_info['phone'] = phone_matches[0]
        
        # 邮箱
        email_match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', resume_text)
        if email_match:
            personal_info['email'] = email_match.group(1)
        
        # 生日
        birthday_match = re.search(r'(\d{4}年\d{1,2}月\d{1,2}日)', resume_text)
        if birthday_match:
            personal_info['birthday'] = birthday_match.group(1)
        
        # 处理简历一的基本信息格式（生日 籍贯 现居地 工作年限1990年5月15日 江苏南京 上海 7年）
        basic_info_match = re.search(r'生日\s+籍贯\s+现居地\s+工作年限(.+)', resume_text)
        if basic_info_match:
            info_line = basic_info_match.group(1).strip()
            # 使用更精确的模式匹配
            info_parts = re.findall(r'(\d{4}年\d{1,2}月\d{1,2}日)|([^\d\s年月日]+(?:[省市区县])?)|(\d+年?)', info_line)
            flat_parts = [part for group in info_parts for part in group if part]
            
            if len(flat_parts) >= 4:
                personal_info['birthday'] = flat_parts[0]
                personal_info['hometown'] = flat_parts[1] 
                personal_info['location'] = flat_parts[2]
                personal_info['work_years'] = flat_parts[3]
        
        # 处理简历二的分行格式
        if '基本信息' in resume_text:
            basic_section = re.search(r'基本信息.*?(?=#|$)', resume_text, re.DOTALL)
            if basic_section:
                basic_text = basic_section.group(0)
                
                # 籍贯
                hometown_match = re.search(r'籍贯\s*([^\n]+)', basic_text)
                if hometown_match:
                    personal_info['hometown'] = self.clean_text(hometown_match.group(1))
                
                # 现居地
                location_match = re.search(r'现居地\s*([^\n]+)', basic_text)
                if location_match:
                    personal_info['location'] = self.clean_text(location_match.group(1))
                
                # 求职意向
                intention_match = re.search(r'求职意向\s*([^\n]+)', basic_text)
                if intention_match:
                    personal_info['job_intention'] = self.clean_text(intention_match.group(1))
        
        # 职位（紧跟姓名后的一行，不是基本信息中的）
        lines = resume_text.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('# ') and personal_info.get('name') in line:
                if i + 1 < len(lines) and not lines[i + 1].startswith('#') and lines[i + 1].strip():
                    next_line = lines[i + 1].strip()
                    # 排除基本信息标题
                    if not any(keyword in next_line for keyword in ['基本信息', '联系方式', '手机', '邮箱']):
                        personal_info['position'] = next_line
                break
        
        return personal_info

    def extract_education(self, resume_text: str) -> List[Dict[str, Any]]:
        """提取教育背景"""
        educations = []
        
        # 查找教育背景部分
        education_section = re.search(r'(?:教育背景|学历背景)(.*?)(?=#\s*(?:工作|项目|技能|专业|自我评价)|$)', 
                                    resume_text, re.DOTALL)
        
        if not education_section:
            return educations
            
        edu_text = education_section.group(1)
        
        # 方法1：处理简历一的格式（# 大学名 \n 时间 \n # 专业/学位）
        university_blocks = re.findall(r'#\s*([^#\n]*(?:大学|学院|University))(.*?)(?=#\s*[^#\n]*(?:大学|学院|University)|$)', edu_text, re.DOTALL)
        
        for university, details in university_blocks:
            education = {
                'university': self.clean_text(university),
                'period': '',
                'major_degree': '',
                'details': []
            }
            
            # 提取时间段
            period_match = re.search(r'(\d{4}\.\d{2}\s*-\s*\d{4}\.\d{2})', details)
            if period_match:
                education['period'] = period_match.group(1)
            
            # 提取专业/学位
            major_match = re.search(r'#\s*([^#\n]+[／/][^#\n]+)', details)
            if major_match:
                education['major_degree'] = self.clean_text(major_match.group(1))
            
            # 提取详细信息
            detail_items = []
            
            # 主修课程
            courses_match = re.search(r'主修课程[：:\s]*([^\n•●·]+)', details)
            if courses_match:
                detail_items.append(f"主修课程：{self.clean_text(courses_match.group(1))}")
            
            # GPA
            gpa_match = re.search(r'GPA[：:\s]*([0-9.]+/[0-9.]+)', details)
            if gpa_match:
                detail_items.append(f"GPA：{gpa_match.group(1)}")
            
            # 排名
            rank_match = re.search(r'排名[前]?\s*([^，,\n]+)', details)
            if rank_match:
                detail_items.append(f"排名：{self.clean_text(rank_match.group(1))}")
            
            # 奖学金
            award_matches = re.findall(r'获得([^•●·\n]*奖学金[^•●·\n]*)', details)
            for award in award_matches:
                detail_items.append(f"奖学金：{self.clean_text(award)}")
            
            # 研究方向
            research_match = re.search(r'研究(?:方向|领域)[：:\s]*([^•●·\n]+)', details)
            if research_match:
                detail_items.append(f"研究方向：{self.clean_text(research_match.group(1))}")
            
            # 论文
            paper_match = re.search(r'发表论文([^•●·\n]+)', details)
            if paper_match:
                detail_items.append(f"发表论文：{self.clean_text(paper_match.group(1))}")
            
            # 项目参与
            project_match = re.search(r'参与([^•●·\n]*项目[^•●·\n]*)', details)
            if project_match:
                detail_items.append(f"项目参与：{self.clean_text(project_match.group(1))}")
            
            education['details'] = detail_items
            educations.append(education)
        
        # 方法2：处理简历二的格式（时间在前，专业在后）
        if not educations:
            # 查找时间-专业对
            time_major_pairs = re.findall(r'(\d{4}\.\d{2}\s*-\s*\d{4}\.\d{2})\s*#\s*([^#\n]+[／/][^#\n]+)', edu_text)
            
            for period, major_degree in time_major_pairs:
                education = {
                    'university': '',  # 简历二中大学信息可能需要从上下文推断
                    'period': period,
                    'major_degree': self.clean_text(major_degree),
                    'details': []
                }
                
                # 查找该时间段对应的详细信息
                section_pattern = rf'{re.escape(period)}.*?(?=\d{{4}}\.\d{{2}}\s*-\s*\d{{4}}\.\d{{2}}|$)'
                section_match = re.search(section_pattern, edu_text, re.DOTALL)
                
                if section_match:
                    section_text = section_match.group(0)
                    
                    # 提取详细信息（与方法1相同的逻辑）
                    detail_items = []
                    
                    courses_match = re.search(r'主修课程[：:\s]*([^\n•●·]+)', section_text)
                    if courses_match:
                        detail_items.append(f"主修课程：{self.clean_text(courses_match.group(1))}")
                    
                    gpa_match = re.search(r'GPA[：:\s]*([0-9.]+/[0-9.]+)', section_text)
                    if gpa_match:
                        detail_items.append(f"GPA：{gpa_match.group(1)}")
                    
                    rank_match = re.search(r'排名[前]?\s*([^，,\n]+)', section_text)
                    if rank_match:
                        detail_items.append(f"排名：{self.clean_text(rank_match.group(1))}")
                    
                    education['details'] = detail_items
                
                educations.append(education)
        
        return educations

    def extract_work_experience(self, resume_text: str) -> List[Dict[str, Any]]:
        """提取工作经历"""
        work_experiences = []
        
        # 查找工作经历部分
        work_section = re.search(r'(?:工作经历|工作经验|工作背景)(.*?)(?=#\s*(?:重点项目|项目|技能|专业|自我评价)|$)', 
                               resume_text, re.DOTALL)
        
        if not work_section:
            return work_experiences
            
        work_text = work_section.group(1)
        
        # 查找公司信息块
        company_blocks = re.findall(r'#\s*([^#\n]*(?:集团|公司|科技|有限公司|Corporation|Inc\.|Ltd\.))(.*?)(?=#\s*[^#\n]*(?:集团|公司|科技|有限公司|Corporation|Inc\.|Ltd\.)|$)', work_text, re.DOTALL)
        
        for company, details in company_blocks:
            work_exp = {
                'company': self.clean_text(company),
                'period': '',
                'position': '',
                'responsibilities': []
            }
            
            # 提取时间段
            period_match = re.search(r'(\d{4}\.\d{2}\s*-\s*(?:\d{4}\.\d{2}|至今))', details)
            if period_match:
                work_exp['period'] = period_match.group(1)
            
            # 提取职位
            position_match = re.search(r'#\s*([^#\n]+(?:经理|总监|专员|工程师|负责人|Manager|Director))', details)
            if position_match:
                work_exp['position'] = self.clean_text(position_match.group(1))
            
            # 提取工作职责（以•●·开头的行）
            responsibilities = re.findall(r'[•●·]\s*([^\n•●·]+)', details)
            work_exp['responsibilities'] = [self.clean_text(resp) for resp in responsibilities if resp.strip()]
            
            work_experiences.append(work_exp)
        
        # 处理简历二的特殊格式（时间在公司前）
        if not work_experiences:
            time_company_blocks = re.findall(r'#\s*(\d{4}\.\d{2}\s*-\s*(?:\d{4}\.\d{2}|至今))\s*#\s*([^#\n]*(?:集团|公司|科技|有限公司))(.*?)(?=#\s*\d{4}\.\d{2}|#\s*技能|$)', work_text, re.DOTALL)
            
            for period, company, details in time_company_blocks:
                work_exp = {
                    'company': self.clean_text(company),
                    'period': period,
                    'position': '',
                    'responsibilities': []
                }
                
                # 提取职位
                position_match = re.search(r'#\s*([^#\n]+(?:经理|总监|专员|工程师|负责人))', details)
                if position_match:
                    work_exp['position'] = self.clean_text(position_match.group(1))
                
                # 提取工作职责
                responsibilities = re.findall(r'[•●·]\s*([^\n•●·]+)', details)
                work_exp['responsibilities'] = [self.clean_text(resp) for resp in responsibilities if resp.strip()]
                
                work_experiences.append(work_exp)
        
        return work_experiences

    def extract_project_experience(self, resume_text: str) -> List[Dict[str, Any]]:
        """提取项目经验"""
        projects = []
        
        # 查找项目经历部分
        project_section = re.search(r'(?:重点项目经历|项目经验|项目经历)(.*?)(?=#\s*(?:专业技能|技能|自我评价)|$)', 
                                  resume_text, re.DOTALL)
        
        if not project_section:
            return projects
            
        project_text = project_section.group(1)
        
        # 查找项目块
        project_blocks = re.findall(r'#\s*([^#\n]+(?:系统|平台|项目))(.*?)(?=#\s*[^#\n]*(?:系统|平台|项目)|$)', project_text, re.DOTALL)
        
        for project_name, details in project_blocks:
            project = {
                'project_name': self.clean_text(project_name),
                'period': '',
                'role': '',
                'company': '',
                'background': '',
                'responsibilities': '',
                'achievements': ''
            }
            
            # 提取时间段
            period_match = re.search(r'(\d{4}\.\d{2}\s*-\s*\d{4}\.\d{2})', details)
            if period_match:
                project['period'] = period_match.group(1)
            
            # 提取角色和公司
            role_company_match = re.search(r'([^|｜\n]+)[|｜]\s*([^#\n]+)', details)
            if role_company_match:
                project['role'] = self.clean_text(role_company_match.group(1))
                project['company'] = self.clean_text(role_company_match.group(2))
            
            # 提取项目背景
            background_match = re.search(r'(?:项目背景|背景)[：:\s]*([^\n#]+)', details)
            if background_match:
                project['background'] = self.clean_text(background_match.group(1))
            
            # 提取项目职责
            responsibilities_match = re.search(r'(?:项目职责|职责)[：:\s]*(.*?)(?:项目成果|成果|#|$)', details, re.DOTALL)
            if responsibilities_match:
                resp_text = responsibilities_match.group(1)
                # 提取职责条目
                resp_items = re.findall(r'[•●·]\s*([^\n•●·]+)', resp_text)
                project['responsibilities'] = '\n'.join([self.clean_text(item) for item in resp_items])
            
            # 提取项目成果
            achievements_match = re.search(r'(?:项目成果|成果)[：:\s]*(.*?)(?:#|$)', details, re.DOTALL)
            if achievements_match:
                achieve_text = achievements_match.group(1)
                # 提取成果条目
                achieve_items = re.findall(r'[•●·]\s*([^\n•●·]+)', achieve_text)
                project['achievements'] = '\n'.join([self.clean_text(item) for item in achieve_items])
            
            projects.append(project)
        
        return projects

    def extract_skills(self, resume_text: str) -> Dict[str, List[str]]:
        """提取专业技能"""
        skills = {
            'technical_skills': [],
            'soft_skills': [],
            'languages': []
        }
        
        # 查找技能部分
        skills_section = re.search(r'(?:专业技能|技能特长|核心技能)(.*?)(?=#\s*(?:自我评价|个人评价)|$)', 
                                 resume_text, re.DOTALL)
        
        if not skills_section:
            return skills
            
        skills_text = skills_section.group(1)
        
        # 从HTML表格中提取技能
        table_skills = re.findall(r'<td>([^<]+)</td>', skills_text)
        if table_skills:
            for skill in table_skills:
                skill = self.clean_text(skill)
                if skill:
                    if re.match(r'^[A-Za-z]', skill) or skill.upper() in ['SQL', 'PYTHON', 'JAVA', 'HTML', 'CSS', 'JS']:
                        skills['technical_skills'].append(skill)
                    else:
                        skills['soft_skills'].append(skill)
        else:
            # 从普通文本中提取技能
            # 分割技能文本
            skill_text = re.sub(r'[•●·]', '', skills_text)  # 移除项目符号
            skill_items = re.split(r'[\s\n]+', skill_text.strip())
            
            for skill in skill_items:
                skill = self.clean_text(skill)
                if not skill or len(skill) < 2:
                    continue
                    
                # 技术技能判断
                if (re.match(r'^[A-Za-z]', skill) or 
                    skill.upper() in ['SQL', 'PYTHON', 'JAVA', 'HTML', 'CSS', 'JAVASCRIPT', 'AXURE', 'JIRA', 'SCRUM'] or
                    any(tech in skill.upper() for tech in ['SQL', 'PYTHON', 'JAVA', 'JS', 'HTML', 'CSS'])):
                    skills['technical_skills'].append(skill)
                else:
                    skills['soft_skills'].append(skill)
        
        # 提取语言技能
        if '英语' in skills_text:
            skills['languages'].append('英语')
        
        # 去重
        skills['technical_skills'] = list(set(skills['technical_skills']))
        skills['soft_skills'] = list(set(skills['soft_skills']))
        skills['languages'] = list(set(skills['languages']))
        
        return skills

    def parse_resume(self, resume_text: str) -> Dict[str, Any]:
        """解析完整简历"""
        resume_data = {
            'personal_info': self.extract_personal_info(resume_text),
            'education': self.extract_education(resume_text),
            'work_experience': self.extract_work_experience(resume_text),
            'project_experience': self.extract_project_experience(resume_text),
            'skills': self.extract_skills(resume_text)
        }
        
        return resume_data


# 测试用例
def test_resume_parser():
    # 简历一
    resume1 = """# 张三
高级项目经理
手机：138-1234-5678
邮箱：<EMAIL>
# 基本信息
生日 籍贯 现居地 工作年限1990年5月15日 江苏南京 上海 7年
# 个人荣誉
• 2020年度优秀员工● 2019年创新项目奖• 2018年行业最佳新人
# 教育背景
# 清华大学
2010.09 -2014.06
# 计算机科学与技术／本科
• 主修课程：数据结构、算法设计、数据库原理、计算机网络
•GPA:3.8/4.0，排名前 $5 \\%$
· 获得校级一等奖学金2次
# 上海交通大学
2014.09-2017.06
# 软件工程/硕士
• 研究方向:人工智能与机器学习· 发表论文2篇，其中1篇被EI收录● 参与国家自然科学基金项目
# 工作经历
# 阿里巴巴集团
• 负责电商平台用户增长产品线，带领5人产品团队，通过数据驱动优化产品策略• 协调研发、设计、运营等多部门资源，确保项目 
按时高质量交付•建立了产品标准化流程，提高团队效率 $2 0 \\%$
# 腾讯科技有限公司
2017.07-2019.06
# 项目经理
· 负责微信小程序生态产品规划，主导B端工具类小程序开发· 通过用户调研和数据分析，优化产品体验，NPS提升25分·协助制定小 
程序商业化策略，实现年收入增长 $5 0 \\%$
# 重点项目经历
# 电商平台用户增长系统
2019.10-2020.06
项目负责人|阿里巴巴集团
# 项目背景：
平台用户增长放缓，新用户获取成本上升，需要建立系统化的用户增长体系。
# 项目职责：
● 主导设计用户裂变增长模型，建立完整的用户增长漏斗，覆盖从获客到留存的全流程• 协调5个部门资源，推动3个核心功能模块开
发，确保项目按时交付●建立数据监控体系，定义关键指标，通过A/B测试实时优化增长策略• 设计用户激励体系，提升用户参与度和
分享意愿
# 项目成果：
•6个月内新用户增长 $4 5 \\%$ ，获客成本降低 $2 8 \\%$ •用戶留存率提升 $1 5 \\%$ ，7日留存达到行业领先水平•年度GMV增长 $3 0 \\%$ ，超额完成业务目标• 系统成为公司用户增长标准解决方案，推广至3个业务线
# 专业技能
<html><body><table><tr><td>项目管理</td><td>团队领导</td><td>风险管理</td><td>敏捷开发</td><td>Scrum</td><td>JIRA</td><td>项目规划</td><td>资源协调</td></tr><tr><td>跨部门协作</td><td>数据分析</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>"""

    # 简历二
    resume2 = """# 张三
# 基本信息
生日
1990年5月15日
籍贯
江苏南京
现居地
上海
求职意向
高级产品经理
# 联系方式
手机
138-1234-5678
邮箱
<EMAIL>
# 教育背景
2010.09 - 2014.06
# 计算机科学与技术／本科
•主修课程：数据结构、算法设计、数据库原理、计算机网络
•GPA:3.8/4.0，排名前 $5 \\%$
•获得校级一等奖学金2次
2014.09 - 2017.06
# 软件工程/硕士
•研究向：智能与机器学习•发表论文2篇，其中1篇被EI收录•参与国家自然科学基金项目
# 工作经验
# 2019.07-至今
# 阿里巴巴集团
# 高级产品经理
•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率 $1 5 \\%$ •带领5人产品团队，协调研发、设计、运
营等多部门资源，确保项目按时高质量交付•通过数据分析优化产品策略，年度GMV增长达 $3 0 \\%$
•建了产品标准化流程，提高团队效率 $2 0 \\%$
# 技能特长
产品规划 需求分析 用户体验设计 数据分析 项目管理 Axure SQL Python
英语流利"""

    parser = ResumeParser()
    
    print("=== 简历一解析结果 ===")
    result1 = parser.parse_resume(resume1)
    print(json.dumps(result1, ensure_ascii=False, indent=2))
    
    print("\n=== 简历二解析结果 ===")
    result2 = parser.parse_resume(resume2)
    print(json.dumps(result2, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test_resume_parser()