#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'resume-ai-backend'))

from app.utils.resume_parser import ResumeParser
import json

# 测试文本
test_text = """![](images/06b2b5e2e17444741675d100b26346911cda859b8b765fb1623fc033e0ef6eaf.jpg)

# 张三

# 基本信息

生日
1990年5月15日
籍贯
江苏南京
现居地
上海
求职意向
高级产品经理

# 联系方式

手机
138-1234-5678
邮箱
<EMAIL>

# 个人荣誉

•2020年度优秀员工•2019年创新项目奖•2018年行业最佳新人

# 教育背景

2010.09 - 2014.06

# 计算机科学与技术／本科

•主修课程：数据结构、算法设计、数据库原理、计算机网络
•GPA:3.8/4.0，排名前 $5 \%$
•获得校级一等奖学金2次

2014.09 - 2017.06

# 软件工程/硕士

•研究向：智能与机器学习•发表论文2篇，其中1篇被EI收录•参与国家自然科学基金项目

# 工作经验

# 2019.07-至今

# 阿里巴巴集团

# 高级产品经理

•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率 $1 5 \%$ •带领5人产品团队，协调研发、设计、运营等多部门资
源，确保项目按时高质量交付•通过数据分析优化产品策略，年度GMV增长达 $3 0 \%$
•建了产品标准化流程，提高团队效率 $2 0 \%$

2017.07 - 2019.06

# 腾讯科技有限公司

# 产品经理

•负责微信小程序生态产品规划，参与设计小程序开放平台功能•主导了2个B端工具类小程序开发，累计服务企业用户10万+• 通过用户调研和数据 
分析，优化产品体验，NPS提升25分•协助制定小程序商业化策略，实现年收入增长 $5 0 \%$

# 技能特长

产品规划 需求分析 用户体验设计 数据分析 项目管理 Axure SQL Python

英语流利

# 自我评价

5年互联网产品经理经验，熟悉从0到1的产品开发全流程。具备敏锐的商业嗅觉和用户洞察力，擅长通过数据驱动产品决策。拥有优秀的跨部门沟 
通能力和团队管理经验，能够在高压环境下高效工作。持续关注行业动态和技术发展趋势，致力于创造有价值的产品体验。"""

def test_parser():
    print("🔍 测试简历解析器...")
    print("=" * 50)

    # 测试工作经验部分匹配
    import re
    work_section_match = re.search(r'# 工作经验\s*\n([\s\S]*?)(?=\n# 技能特长|\n# 自我评价|$)', test_text)
    if work_section_match:
        print("✅ 找到工作经验部分:")
        work_content = work_section_match.group(1)
        print(f"工作经验部分长度: {len(work_content)}")
        print("前500字符:")
        print(repr(work_content[:500]))
    else:
        print("❌ 未找到工作经验部分")

    # 测试教育背景部分匹配
    edu_section_match = re.search(r'# 教育背景\s*\n([\s\S]*?)(?=\n# [^#\d]|$)', test_text)
    if edu_section_match:
        print("✅ 找到教育背景部分:")
        print(repr(edu_section_match.group(1)[:200]))
    else:
        print("❌ 未找到教育背景部分")

    print("\n" + "=" * 50)

    # 解析简历
    result = ResumeParser.parse_resume(test_text)

    # 打印结果
    print("📋 解析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

    print("\n" + "=" * 50)
    print("📊 解析统计:")
    print(f"基本信息字段数: {len(result['基本信息'])}")
    print(f"工作经历数量: {len(result['工作经历'])}")
    print(f"项目经历数量: {len(result['项目经历'])}")
    print(f"教育经历数量: {len(result['教育经历'])}")
    print(f"技能数量: {len(result['技能'])}")

if __name__ == "__main__":
    test_parser()
