[{"layout_dets": [{"category_id": 0, "poly": [126, 1457, 251, 1457, 251, 1497, 126, 1497], "score": 0.91}, {"category_id": 1, "poly": [165, 2092, 961, 2092, 961, 2226, 165, 2226], "score": 0.893}, {"category_id": 0, "poly": [185, 1864, 342, 1864, 342, 1911, 185, 1911], "score": 0.892}, {"category_id": 0, "poly": [125, 1100, 252, 1100, 252, 1138, 125, 1138], "score": 0.885}, {"category_id": 2, "poly": [64, 39, 230, 39, 230, 68, 64, 68], "score": 0.875}, {"category_id": 2, "poly": [1558, 2275, 1593, 2275, 1593, 2301, 1558, 2301], "score": 0.867}, {"category_id": 3, "poly": [720, 110, 941, 110, 941, 332, 720, 332], "score": 0.837}, {"category_id": 0, "poly": [159, 2038, 525, 2038, 525, 2081, 159, 2081], "score": 0.825}, {"category_id": 1, "poly": [156, 1975, 441, 1975, 441, 2016, 156, 2016], "score": 0.776}, {"category_id": 0, "poly": [126, 524, 251, 524, 251, 563, 126, 563], "score": 0.757}, {"category_id": 1, "poly": [147, 1537, 427, 1537, 427, 1708, 147, 1708], "score": 0.709}, {"category_id": 2, "poly": [67, 2270, 651, 2270, 651, 2302, 67, 2302], "score": 0.707}, {"category_id": 2, "poly": [904, 41, 999, 41, 999, 71, 904, 71], "score": 0.706}, {"category_id": 0, "poly": [783, 406, 875, 406, 875, 458, 783, 458], "score": 0.543}, {"category_id": 1, "poly": [1369, 1973, 1511, 1973, 1511, 2018, 1369, 2018], "score": 0.517}, {"category_id": 1, "poly": [135, 601, 332, 601, 332, 1017, 135, 1017], "score": 0.374}, {"category_id": 1, "poly": [134, 1179, 466, 1179, 466, 1373, 134, 1373], "score": 0.27}, {"category_id": 2, "poly": [1369, 1973, 1511, 1973, 1511, 2018, 1369, 2018], "score": 0.246}, {"category_id": 1, "poly": [783, 406, 875, 406, 875, 458, 783, 458], "score": 0.216}, {"category_id": 2, "poly": [123, 1867, 172, 1867, 172, 1908, 123, 1908], "score": 0.211}, {"category_id": 1, "poly": [67, 2270, 651, 2270, 651, 2302, 67, 2302], "score": 0.204}, {"category_id": 6, "poly": [126, 524, 251, 524, 251, 563, 126, 563], "score": 0.111}, {"category_id": 13, "poly": [472, 2140, 519, 2140, 519, 2175, 472, 2175], "score": 0.85, "latex": "5 \\%"}, {"category_id": 15, "poly": [120.0, 1451.0, 257.0, 1451.0, 257.0, 1504.0, 120.0, 1504.0], "score": 0.998, "text": "个人荣誉"}, {"category_id": 15, "poly": [120.0, 1096.0, 256.0, 1096.0, 256.0, 1142.0, 120.0, 1142.0], "score": 0.999, "text": "联系方式"}, {"category_id": 15, "poly": [120.0, 518.0, 257.0, 518.0, 257.0, 568.0, 120.0, 568.0], "score": 1.0, "text": "基本信息"}, {"category_id": 15, "poly": [1366.0, 1972.0, 1514.0, 1972.0, 1514.0, 2021.0, 1366.0, 2021.0], "score": 1.0, "text": "清华大学"}, {"category_id": 15, "poly": [120.0, 518.0, 257.0, 518.0, 257.0, 568.0, 120.0, 568.0], "score": 1.0, "text": "基本信息"}, {"category_id": 15, "poly": [1366.0, 1972.0, 1514.0, 1972.0, 1514.0, 2021.0, 1366.0, 2021.0], "score": 1.0, "text": "清华大学"}, {"category_id": 15, "poly": [183.0, 1862.0, 347.0, 1862.0, 347.0, 1915.0, 183.0, 1915.0], "score": 1.0, "text": "教育背景"}, {"category_id": 15, "poly": [64.0, 40.0, 231.0, 40.0, 231.0, 69.0, 64.0, 69.0], "score": 0.988, "text": "2025/7/23 10:44"}, {"category_id": 15, "poly": [1554.0, 2272.0, 1596.0, 2272.0, 1596.0, 2307.0, 1554.0, 2307.0], "score": 0.992, "text": "1/2"}, {"category_id": 16, "poly": [753.0, 153.0, 905.0, 153.0, 905.0, 299.0, 753.0, 299.0], "score": 0.297, "text": "8"}, {"category_id": 16, "poly": [924.0, 241.0, 932.0, 241.0, 932.0, 258.0, 924.0, 258.0], "score": 0.0, "text": ""}, {"category_id": 16, "poly": [912.75, 262.5, 929.75, 262.5, 929.75, 266.5, 912.75, 266.5], "score": 0.0, "text": ""}, {"category_id": 16, "poly": [904.0, 276.0, 922.0, 276.0, 922.0, 281.0, 904.0, 281.0], "score": 0.0, "text": ""}, {"category_id": 15, "poly": [155.0, 2037.0, 523.0, 2037.0, 523.0, 2081.0, 155.0, 2081.0], "score": 0.978, "text": "计算机科学与技术／本科"}, {"category_id": 15, "poly": [64.0, 2271.0, 651.0, 2271.0, 651.0, 2302.0, 64.0, 2302.0], "score": 0.971, "text": "file://C:/Users/<USER>/Desktop/Al简历/学生简历.html"}, {"category_id": 15, "poly": [64.0, 2271.0, 651.0, 2271.0, 651.0, 2302.0, 64.0, 2302.0], "score": 0.971, "text": "file://C:/Users/<USER>/Desktop/Al简历/学生简历.html"}, {"category_id": 15, "poly": [901.0, 39.0, 1001.0, 39.0, 1001.0, 76.0, 901.0, 76.0], "score": 0.999, "text": "专业简历"}, {"category_id": 15, "poly": [775.0, 398.0, 883.0, 398.0, 883.0, 466.0, 775.0, 466.0], "score": 0.999, "text": "张三"}, {"category_id": 15, "poly": [775.0, 398.0, 883.0, 398.0, 883.0, 466.0, 775.0, 466.0], "score": 0.999, "text": "张三"}, {"category_id": 15, "poly": [131.0, 1886.0, 148.0, 1886.0, 148.0, 1902.0, 131.0, 1902.0], "score": 0.868, "text": "「"}, {"category_id": 15, "poly": [163.0, 2094.0, 956.0, 2094.0, 956.0, 2130.0, 163.0, 2130.0], "score": 0.99, "text": "•主修课程：数据结构、算法设计、数据库原理、计算机网络"}, {"category_id": 15, "poly": [165.0, 2140.0, 471.0, 2140.0, 471.0, 2177.0, 165.0, 2177.0], "score": 0.972, "text": "•GPA:3.8/4.0，排名前"}, {"category_id": 15, "poly": [165.0, 2187.0, 506.0, 2187.0, 506.0, 2225.0, 165.0, 2225.0], "score": 0.982, "text": "•获得校级一等奖学金2次"}, {"category_id": 15, "poly": [154.0, 1974.0, 439.0, 1974.0, 439.0, 2019.0, 154.0, 2019.0], "score": 0.962, "text": "2010.09 - 2014.06"}, {"category_id": 15, "poly": [144.0, 1539.0, 405.0, 1539.0, 405.0, 1590.0, 144.0, 1590.0], "score": 0.981, "text": "•2020年度优秀员工"}, {"category_id": 15, "poly": [144.0, 1604.0, 406.0, 1604.0, 406.0, 1655.0, 144.0, 1655.0], "score": 0.988, "text": "•2019年创新项目奖"}, {"category_id": 15, "poly": [145.0, 1670.0, 428.0, 1670.0, 428.0, 1712.0, 145.0, 1712.0], "score": 0.943, "text": "•2018年行业最佳新人"}, {"category_id": 15, "poly": [137.0, 603.0, 201.0, 603.0, 201.0, 647.0, 137.0, 647.0], "score": 0.996, "text": "生日"}, {"category_id": 15, "poly": [138.0, 652.0, 330.0, 652.0, 330.0, 687.0, 138.0, 687.0], "score": 1.0, "text": "1990年5月15日"}, {"category_id": 15, "poly": [136.0, 713.0, 202.0, 713.0, 202.0, 751.0, 136.0, 751.0], "score": 0.999, "text": "籍贯"}, {"category_id": 15, "poly": [137.0, 758.0, 256.0, 758.0, 256.0, 797.0, 137.0, 797.0], "score": 1.0, "text": "江苏南京"}, {"category_id": 15, "poly": [138.0, 823.0, 229.0, 823.0, 229.0, 860.0, 138.0, 860.0], "score": 1.0, "text": "现居地"}, {"category_id": 15, "poly": [138.0, 866.0, 203.0, 866.0, 203.0, 906.0, 138.0, 906.0], "score": 1.0, "text": "上海"}, {"category_id": 15, "poly": [138.0, 931.0, 256.0, 931.0, 256.0, 970.0, 138.0, 970.0], "score": 1.0, "text": "求职意向"}, {"category_id": 15, "poly": [139.0, 977.0, 309.0, 977.0, 309.0, 1012.0, 139.0, 1012.0], "score": 1.0, "text": "高级产品经理"}, {"category_id": 15, "poly": [135.0, 1178.0, 206.0, 1178.0, 206.0, 1221.0, 135.0, 1221.0], "score": 1.0, "text": "手机"}, {"category_id": 15, "poly": [138.0, 1226.0, 332.0, 1226.0, 332.0, 1260.0, 138.0, 1260.0], "score": 0.999, "text": "138-1234-5678"}, {"category_id": 15, "poly": [135.0, 1285.0, 204.0, 1285.0, 204.0, 1329.0, 135.0, 1329.0], "score": 1.0, "text": "邮箱"}, {"category_id": 15, "poly": [139.0, 1335.0, 461.0, 1335.0, 461.0, 1373.0, 139.0, 1373.0], "score": 0.999, "text": "<EMAIL>"}], "page_info": {"page_no": 0, "width": 1654, "height": 2339}}, {"layout_dets": [{"category_id": 1, "poly": [153, 1806, 1509, 1806, 1509, 1943, 153, 1943], "score": 0.975}, {"category_id": 1, "poly": [165, 657, 1269, 657, 1269, 837, 165, 837], "score": 0.97}, {"category_id": 1, "poly": [165, 1128, 987, 1128, 987, 1309, 165, 1309], "score": 0.964}, {"category_id": 1, "poly": [165, 224, 609, 224, 609, 357, 165, 357], "score": 0.961}, {"category_id": 2, "poly": [67, 2271, 650, 2271, 650, 2301, 67, 2301], "score": 0.924}, {"category_id": 0, "poly": [187, 1698, 343, 1698, 343, 1746, 187, 1746], "score": 0.911}, {"category_id": 0, "poly": [157, 1077, 297, 1077, 297, 1116, 157, 1116], "score": 0.884}, {"category_id": 2, "poly": [1556, 2275, 1592, 2275, 1592, 2300, 1556, 2300], "score": 0.883}, {"category_id": 0, "poly": [188, 429, 344, 429, 344, 476, 188, 476], "score": 0.882}, {"category_id": 0, "poly": [157, 171, 391, 171, 391, 212, 157, 212], "score": 0.88}, {"category_id": 1, "poly": [156, 107, 439, 107, 439, 147, 156, 147], "score": 0.878}, {"category_id": 0, "poly": [158, 605, 364, 605, 364, 646, 158, 646], "score": 0.863}, {"category_id": 0, "poly": [1301, 538, 1511, 538, 1511, 582, 1301, 582], "score": 0.816}, {"category_id": 0, "poly": [172, 1381, 329, 1381, 329, 1430, 172, 1430], "score": 0.79}, {"category_id": 1, "poly": [156, 1011, 439, 1011, 439, 1052, 156, 1052], "score": 0.769}, {"category_id": 0, "poly": [155, 539, 380, 539, 380, 581, 155, 581], "score": 0.746}, {"category_id": 1, "poly": [177, 1579, 292, 1579, 292, 1615, 177, 1615], "score": 0.721}, {"category_id": 2, "poly": [64, 39, 231, 39, 231, 68, 64, 68], "score": 0.67}, {"category_id": 0, "poly": [1234, 1009, 1510, 1009, 1510, 1055, 1234, 1055], "score": 0.651}, {"category_id": 3, "poly": [122, 1700, 171, 1700, 171, 1744, 122, 1744], "score": 0.465}, {"category_id": 0, "poly": [1306, 104, 1510, 104, 1510, 149, 1306, 149], "score": 0.439}, {"category_id": 2, "poly": [123, 430, 171, 430, 171, 476, 123, 476], "score": 0.429}, {"category_id": 1, "poly": [173, 1493, 1444, 1493, 1444, 1537, 173, 1537], "score": 0.377}, {"category_id": 0, "poly": [904, 40, 999, 40, 999, 72, 904, 72], "score": 0.311}, {"category_id": 1, "poly": [1234, 1009, 1510, 1009, 1510, 1055, 1234, 1055], "score": 0.295}, {"category_id": 2, "poly": [904, 40, 999, 40, 999, 72, 904, 72], "score": 0.258}, {"category_id": 1, "poly": [1306, 104, 1510, 104, 1510, 149, 1306, 149], "score": 0.247}, {"category_id": 1, "poly": [64, 39, 231, 39, 231, 68, 64, 68], "score": 0.245}, {"category_id": 2, "poly": [1306, 104, 1510, 104, 1510, 149, 1306, 149], "score": 0.244}, {"category_id": 3, "poly": [123, 430, 171, 430, 171, 476, 123, 476], "score": 0.226}, {"category_id": 2, "poly": [122, 1700, 171, 1700, 171, 1744, 122, 1744], "score": 0.219}, {"category_id": 1, "poly": [904, 40, 999, 40, 999, 72, 904, 72], "score": 0.207}, {"category_id": 3, "poly": [121, 1380, 160, 1380, 160, 1432, 121, 1432], "score": 0.185}, {"category_id": 1, "poly": [155, 539, 380, 539, 380, 581, 155, 581], "score": 0.183}, {"category_id": 2, "poly": [121, 1380, 160, 1380, 160, 1432, 121, 1432], "score": 0.156}, {"category_id": 13, "poly": [689, 799, 753, 799, 753, 834, 689, 834], "score": 0.89, "latex": "2 0 \\%"}, {"category_id": 13, "poly": [780, 753, 843, 753, 843, 788, 780, 788], "score": 0.88, "latex": "3 0 \\%"}, {"category_id": 13, "poly": [777, 1270, 841, 1270, 841, 1305, 777, 1305], "score": 0.88, "latex": "5 0 \\%"}, {"category_id": 13, "poly": [1203, 658, 1266, 658, 1266, 694, 1203, 694], "score": 0.87, "latex": "1 5 \\%"}, {"category_id": 15, "poly": [154.0, 1076.0, 298.0, 1076.0, 298.0, 1119.0, 154.0, 1119.0], "score": 1.0, "text": "产品经理"}, {"category_id": 15, "poly": [185.0, 1697.0, 347.0, 1697.0, 347.0, 1753.0, 185.0, 1753.0], "score": 1.0, "text": "自我评价"}, {"category_id": 15, "poly": [183.0, 428.0, 347.0, 428.0, 347.0, 481.0, 183.0, 481.0], "score": 1.0, "text": "工作经验"}, {"category_id": 15, "poly": [169.0, 1381.0, 334.0, 1381.0, 334.0, 1433.0, 169.0, 1433.0], "score": 1.0, "text": "技能特长"}, {"category_id": 15, "poly": [64.0, 40.0, 231.0, 40.0, 231.0, 69.0, 64.0, 69.0], "score": 0.988, "text": "2025/7/23 10:44"}, {"category_id": 15, "poly": [64.0, 40.0, 231.0, 40.0, 231.0, 69.0, 64.0, 69.0], "score": 0.988, "text": "2025/7/23 10:44"}, {"category_id": 15, "poly": [1552.0, 2272.0, 1596.0, 2272.0, 1596.0, 2306.0, 1552.0, 2306.0], "score": 0.939, "text": "2/2"}, {"category_id": 15, "poly": [63.0, 2271.0, 651.0, 2271.0, 651.0, 2302.0, 63.0, 2302.0], "score": 0.97, "text": "file:///C:/Users/<USER>/Desktop/Al简历/学生简历.html"}, {"category_id": 15, "poly": [902.0, 40.0, 1000.0, 40.0, 1000.0, 73.0, 902.0, 73.0], "score": 0.999, "text": "专业简历"}, {"category_id": 15, "poly": [902.0, 40.0, 1000.0, 40.0, 1000.0, 73.0, 902.0, 73.0], "score": 0.999, "text": "专业简历"}, {"category_id": 15, "poly": [175.0, 1578.0, 293.0, 1578.0, 293.0, 1617.0, 175.0, 1617.0], "score": 1.0, "text": "英语流利"}, {"category_id": 15, "poly": [902.0, 40.0, 1000.0, 40.0, 1000.0, 73.0, 902.0, 73.0], "score": 0.999, "text": "专业简历"}, {"category_id": 16, "poly": [131.75, 1701.0, 165.75, 1701.0, 165.75, 1740.0, 131.75, 1740.0], "score": 0.0, "text": ""}, {"category_id": 16, "poly": [131.75, 1701.0, 165.75, 1701.0, 165.75, 1740.0, 131.75, 1740.0], "score": 0.0, "text": ""}, {"category_id": 15, "poly": [153.0, 168.0, 393.0, 168.0, 393.0, 215.0, 153.0, 215.0], "score": 0.966, "text": "软件工程/硕士"}, {"category_id": 15, "poly": [152.0, 536.0, 383.0, 536.0, 383.0, 587.0, 152.0, 587.0], "score": 0.993, "text": "2019.07-至今"}, {"category_id": 15, "poly": [152.0, 536.0, 383.0, 536.0, 383.0, 587.0, 152.0, 587.0], "score": 0.993, "text": "2019.07-至今"}, {"category_id": 15, "poly": [156.0, 606.0, 364.0, 606.0, 364.0, 645.0, 156.0, 645.0], "score": 1.0, "text": "高级产品经理"}, {"category_id": 15, "poly": [1303.0, 541.0, 1510.0, 541.0, 1510.0, 583.0, 1303.0, 583.0], "score": 0.999, "text": "阿里巴巴集团"}, {"category_id": 15, "poly": [1303.0, 104.0, 1513.0, 104.0, 1513.0, 152.0, 1303.0, 152.0], "score": 1.0, "text": "上海交通大学"}, {"category_id": 15, "poly": [1303.0, 104.0, 1513.0, 104.0, 1513.0, 152.0, 1303.0, 152.0], "score": 1.0, "text": "上海交通大学"}, {"category_id": 15, "poly": [1303.0, 104.0, 1513.0, 104.0, 1513.0, 152.0, 1303.0, 152.0], "score": 1.0, "text": "上海交通大学"}, {"category_id": 15, "poly": [1237.0, 1012.0, 1509.0, 1012.0, 1509.0, 1053.0, 1237.0, 1053.0], "score": 0.999, "text": "腾讯科技有限公司"}, {"category_id": 15, "poly": [154.0, 106.0, 440.0, 106.0, 440.0, 150.0, 154.0, 150.0], "score": 0.961, "text": "2014.09 - 2017.06"}, {"category_id": 15, "poly": [154.0, 1011.0, 440.0, 1011.0, 440.0, 1056.0, 154.0, 1056.0], "score": 0.948, "text": "2017.07 - 2019.06"}, {"category_id": 15, "poly": [1237.0, 1012.0, 1509.0, 1012.0, 1509.0, 1053.0, 1237.0, 1053.0], "score": 0.999, "text": "腾讯科技有限公司"}, {"category_id": 15, "poly": [160.0, 1814.0, 1494.0, 1814.0, 1494.0, 1844.0, 160.0, 1844.0], "score": 0.999, "text": "5年互联网产品经理经验，熟悉从0到1的产品开发全流程。具备敏锐的商业嗅觉和用户洞察力，擅长通过"}, {"category_id": 15, "poly": [157.0, 1857.0, 1506.0, 1857.0, 1506.0, 1892.0, 157.0, 1892.0], "score": 1.0, "text": "数据驱动产品决策。拥有优秀的跨部门沟通能力和团队管理经验，能够在高压环境下高效工作。持续关注"}, {"category_id": 15, "poly": [157.0, 1905.0, 905.0, 1905.0, 905.0, 1940.0, 157.0, 1940.0], "score": 1.0, "text": "行业动态和技术发展趋势，致力于创造有价值的产品体验。"}, {"category_id": 15, "poly": [162.0, 657.0, 1202.0, 657.0, 1202.0, 694.0, 162.0, 694.0], "score": 0.976, "text": "•负责电商平台用户增长产品线，主导设计了3个核心功能模块，提升用户留存率"}, {"category_id": 15, "poly": [159.0, 701.0, 1266.0, 701.0, 1266.0, 743.0, 159.0, 743.0], "score": 0.989, "text": "•带领5人产品团队，协调研发、设计、运营等多部门资源，确保项目按时高质量交付"}, {"category_id": 15, "poly": [162.0, 751.0, 779.0, 751.0, 779.0, 788.0, 162.0, 788.0], "score": 0.978, "text": "•通过数据分析优化产品策略，年度GMV增长达"}, {"category_id": 15, "poly": [163.0, 797.0, 688.0, 797.0, 688.0, 836.0, 163.0, 836.0], "score": 0.98, "text": "•建了产品标准化流程，提高团队效率"}, {"category_id": 15, "poly": [163.0, 1130.0, 986.0, 1130.0, 986.0, 1166.0, 163.0, 1166.0], "score": 0.988, "text": "•负责微信小程序生态产品规划，参与设计小程序开放平台功能"}, {"category_id": 15, "poly": [163.0, 1176.0, 954.0, 1176.0, 954.0, 1213.0, 163.0, 1213.0], "score": 0.988, "text": "•主导了2个B端工具类小程序开发，累计服务企业用户10万+"}, {"category_id": 15, "poly": [165.0, 1224.0, 927.0, 1224.0, 927.0, 1261.0, 165.0, 1261.0], "score": 0.979, "text": "• 通过用户调研和数据分析，优化产品体验，NPS提升25分"}, {"category_id": 15, "poly": [165.0, 1270.0, 776.0, 1270.0, 776.0, 1307.0, 165.0, 1307.0], "score": 0.985, "text": "•协助制定小程序商业化策略，实现年收入增长"}, {"category_id": 15, "poly": [164.0, 224.0, 606.0, 224.0, 606.0, 263.0, 164.0, 263.0], "score": 0.989, "text": "•研究向：智能与机器学习"}, {"category_id": 15, "poly": [165.0, 271.0, 606.0, 271.0, 606.0, 307.0, 165.0, 307.0], "score": 0.977, "text": "•发表论文2篇，其中1篇被EI收录"}, {"category_id": 15, "poly": [164.0, 319.0, 546.0, 319.0, 546.0, 356.0, 164.0, 356.0], "score": 0.986, "text": "•参与国家自然科学基金项目"}, {"category_id": 15, "poly": [173.0, 1491.0, 296.0, 1491.0, 296.0, 1540.0, 173.0, 1540.0], "score": 1.0, "text": "产品规划"}, {"category_id": 15, "poly": [348.0, 1496.0, 464.0, 1496.0, 464.0, 1535.0, 348.0, 1535.0], "score": 0.999, "text": "需求分析"}, {"category_id": 15, "poly": [519.0, 1496.0, 688.0, 1496.0, 688.0, 1535.0, 519.0, 1535.0], "score": 1.0, "text": "用户体验设计"}, {"category_id": 15, "poly": [742.0, 1493.0, 863.0, 1493.0, 863.0, 1536.0, 742.0, 1536.0], "score": 1.0, "text": "数据分析"}, {"category_id": 15, "poly": [913.0, 1496.0, 1033.0, 1496.0, 1033.0, 1535.0, 913.0, 1535.0], "score": 1.0, "text": "项目管理"}, {"category_id": 15, "poly": [1083.0, 1498.0, 1172.0, 1498.0, 1172.0, 1538.0, 1083.0, 1538.0], "score": 0.997, "text": "Axure"}, {"category_id": 15, "poly": [1214.0, 1495.0, 1295.0, 1495.0, 1295.0, 1541.0, 1214.0, 1541.0], "score": 0.995, "text": "SQL"}, {"category_id": 15, "poly": [1318.0, 1493.0, 1435.0, 1493.0, 1435.0, 1543.0, 1318.0, 1543.0], "score": 0.999, "text": "Python"}], "page_info": {"page_no": 1, "width": 1654, "height": 2339}}]